"""
Data processing utilities for the AiBiao AI Data Analysis System.
"""

import pandas as pd
import numpy as np
import json
import io
import tempfile
import os
from typing import Dict, List, Optional, Any, Tuple, Union
from pathlib import Path
import requests

class DataProcessor:
    """Handles data processing and file operations."""
    
    def __init__(self):
        self.supported_formats = ['.csv', '.xlsx', '.xls', '.json']
        self.max_file_size = 100 * 1024 * 1024  # 100MB
    
    def process_file_upload(self, file_path: str) -> Tuple[bool, Union[pd.DataFrame, str]]:
        """
        Process an uploaded file and return a DataFrame.
        
        Args:
            file_path: Path to the uploaded file
            
        Returns:
            Tuple of (success, dataframe_or_error_message)
        """
        try:
            if not os.path.exists(file_path):
                return False, "File not found"
            
            # Check file size
            file_size = os.path.getsize(file_path)
            if file_size > self.max_file_size:
                return False, f"File too large. Maximum size is {self.max_file_size // (1024*1024)}MB"
            
            # Get file extension
            file_ext = Path(file_path).suffix.lower()
            
            if file_ext not in self.supported_formats:
                return False, f"Unsupported file format. Supported formats: {', '.join(self.supported_formats)}"
            
            # Process based on file type
            if file_ext == '.csv':
                df = pd.read_csv(file_path, encoding='utf-8')
            elif file_ext in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            elif file_ext == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                df = pd.json_normalize(data)
            else:
                return False, f"Unsupported file format: {file_ext}"
            
            # Basic validation
            if df.empty:
                return False, "File is empty or contains no valid data"
            
            # Clean column names
            df.columns = df.columns.astype(str)
            df.columns = [col.strip() for col in df.columns]
            
            return True, df
            
        except Exception as e:
            return False, f"Error processing file: {str(e)}"
    
    def process_url_data(self, url: str) -> Tuple[bool, Union[pd.DataFrame, str]]:
        """
        Download and process data from a URL.
        
        Args:
            url: URL to download data from
            
        Returns:
            Tuple of (success, dataframe_or_error_message)
        """
        try:
            # Validate URL
            if not url.startswith(('http://', 'https://')):
                return False, "Invalid URL. Must start with http:// or https://"
            
            # Download file
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # Check content size
            if len(response.content) > self.max_file_size:
                return False, f"File too large. Maximum size is {self.max_file_size // (1024*1024)}MB"
            
            # Determine file type from URL or content-type
            content_type = response.headers.get('content-type', '').lower()
            
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
                tmp_file.write(response.content)
                tmp_path = tmp_file.name
            
            try:
                # Try to determine format and process
                if 'csv' in content_type or url.endswith('.csv'):
                    df = pd.read_csv(io.StringIO(response.text))
                elif 'excel' in content_type or url.endswith(('.xlsx', '.xls')):
                    df = pd.read_excel(io.BytesIO(response.content))
                elif 'json' in content_type or url.endswith('.json'):
                    data = response.json()
                    df = pd.json_normalize(data)
                else:
                    # Try CSV as default
                    df = pd.read_csv(io.StringIO(response.text))
                
                # Clean column names
                df.columns = df.columns.astype(str)
                df.columns = [col.strip() for col in df.columns]
                
                return True, df
                
            finally:
                # Clean up temporary file
                os.unlink(tmp_path)
                
        except requests.RequestException as e:
            return False, f"Error downloading file: {str(e)}"
        except Exception as e:
            return False, f"Error processing data: {str(e)}"
    
    def process_direct_input(self, data_text: str) -> Tuple[bool, Union[pd.DataFrame, str]]:
        """
        Process directly input CSV data.
        
        Args:
            data_text: CSV data as text
            
        Returns:
            Tuple of (success, dataframe_or_error_message)
        """
        try:
            if not data_text.strip():
                return False, "No data provided"
            
            # Try to parse as CSV
            df = pd.read_csv(io.StringIO(data_text))
            
            if df.empty:
                return False, "No valid data found"
            
            # Clean column names
            df.columns = df.columns.astype(str)
            df.columns = [col.strip() for col in df.columns]
            
            return True, df
            
        except Exception as e:
            return False, f"Error parsing data: {str(e)}"
    
    def upload_file_to_temp_url(self, file_path: str) -> Tuple[bool, str]:
        """
        Upload a file to a temporary URL for API usage.
        This is a placeholder - in a real implementation, you'd upload to a cloud service.
        
        Args:
            file_path: Path to the file to upload
            
        Returns:
            Tuple of (success, url_or_error_message)
        """
        try:
            # For demo purposes, we'll simulate a file upload
            # In a real implementation, you'd upload to AWS S3, Google Cloud Storage, etc.
            
            # For now, return a placeholder URL
            filename = os.path.basename(file_path)
            temp_url = f"https://temp-storage.example.com/{filename}"
            
            return True, temp_url
            
        except Exception as e:
            return False, f"Error uploading file: {str(e)}"
    
    def get_data_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Generate a summary of the DataFrame.
        
        Args:
            df: DataFrame to summarize
            
        Returns:
            Dictionary containing data summary
        """
        try:
            summary = {
                "shape": df.shape,
                "columns": list(df.columns),
                "dtypes": df.dtypes.to_dict(),
                "missing_values": df.isnull().sum().to_dict(),
                "numeric_columns": list(df.select_dtypes(include=[np.number]).columns),
                "categorical_columns": list(df.select_dtypes(include=['object']).columns),
                "memory_usage": df.memory_usage(deep=True).sum(),
                "sample_data": df.head(5).to_dict('records')
            }
            
            # Add basic statistics for numeric columns
            if summary["numeric_columns"]:
                summary["numeric_stats"] = df[summary["numeric_columns"]].describe().to_dict()
            
            return summary
            
        except Exception as e:
            return {"error": f"Error generating summary: {str(e)}"}
    
    def export_dataframe(self, df: pd.DataFrame, format: str = 'csv') -> Tuple[bool, str]:
        """
        Export DataFrame to specified format.
        
        Args:
            df: DataFrame to export
            format: Export format ('csv', 'xlsx', 'json')
            
        Returns:
            Tuple of (success, file_path_or_error_message)
        """
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{format}') as tmp_file:
                if format == 'csv':
                    df.to_csv(tmp_file.name, index=False)
                elif format == 'xlsx':
                    df.to_excel(tmp_file.name, index=False)
                elif format == 'json':
                    df.to_json(tmp_file.name, orient='records', indent=2)
                else:
                    return False, f"Unsupported export format: {format}"
                
                return True, tmp_file.name
                
        except Exception as e:
            return False, f"Error exporting data: {str(e)}"
