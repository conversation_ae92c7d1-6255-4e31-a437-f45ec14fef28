"""
Error handling and validation utilities for the AiBiao AI Data Analysis System.
"""

import logging
import traceback
import functools
from typing import Dict, List, Optional, Any, Tuple, Callable
import pandas as pd
import requests
from pathlib import Path
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('aibiao_system.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Custom exception for validation errors."""
    pass

class APIError(Exception):
    """Custom exception for API-related errors."""
    pass

class DataProcessingError(Exception):
    """Custom exception for data processing errors."""
    pass

class ErrorHandler:
    """Centralized error handling and validation."""
    
    def __init__(self):
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        self.supported_file_types = ['.csv', '.xlsx', '.xls', '.json']
        self.api_key_pattern = re.compile(r'^sk-[a-zA-Z0-9]{32,}$')
    
    def handle_errors(self, func: Callable) -> Callable:
        """Decorator for handling errors in functions."""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ValidationError as e:
                logger.warning(f"Validation error in {func.__name__}: {str(e)}")
                return self._create_error_response(f"Validation Error: {str(e)}")
            except APIError as e:
                logger.error(f"API error in {func.__name__}: {str(e)}")
                return self._create_error_response(f"API Error: {str(e)}")
            except DataProcessingError as e:
                logger.error(f"Data processing error in {func.__name__}: {str(e)}")
                return self._create_error_response(f"Data Processing Error: {str(e)}")
            except Exception as e:
                logger.error(f"Unexpected error in {func.__name__}: {str(e)}\n{traceback.format_exc()}")
                return self._create_error_response(f"Unexpected Error: {str(e)}")
        return wrapper
    
    def validate_api_key(self, api_key: str) -> Tuple[bool, str]:
        """Validate API key format."""
        if not api_key:
            return False, "API key is required"
        
        if not isinstance(api_key, str):
            return False, "API key must be a string"
        
        if len(api_key.strip()) == 0:
            return False, "API key cannot be empty"
        
        # Basic format validation (adjust pattern as needed)
        if not api_key.startswith('sk-'):
            return False, "API key must start with 'sk-'"
        
        if len(api_key) < 35:  # Minimum reasonable length
            return False, "API key appears to be too short"
        
        return True, "Valid API key format"
    
    def validate_file_upload(self, file_path: str) -> Tuple[bool, str]:
        """Validate uploaded file."""
        if not file_path:
            return False, "No file provided"
        
        file_path = Path(file_path)
        
        # Check if file exists
        if not file_path.exists():
            return False, "File does not exist"
        
        # Check file extension
        if file_path.suffix.lower() not in self.supported_file_types:
            return False, f"Unsupported file type. Supported types: {', '.join(self.supported_file_types)}"
        
        # Check file size
        file_size = file_path.stat().st_size
        if file_size > self.max_file_size:
            return False, f"File too large. Maximum size: {self.max_file_size // (1024*1024)}MB"
        
        if file_size == 0:
            return False, "File is empty"
        
        return True, "File validation passed"
    
    def validate_url(self, url: str) -> Tuple[bool, str]:
        """Validate URL format and accessibility."""
        if not url:
            return False, "URL is required"
        
        if not isinstance(url, str):
            return False, "URL must be a string"
        
        url = url.strip()
        
        # Basic URL format validation
        if not url.startswith(('http://', 'https://')):
            return False, "URL must start with http:// or https://"
        
        # Check for common URL patterns
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if not url_pattern.match(url):
            return False, "Invalid URL format"
        
        return True, "URL validation passed"
    
    def validate_dataframe(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """Validate DataFrame for analysis."""
        if df is None:
            return False, "DataFrame is None"
        
        if not isinstance(df, pd.DataFrame):
            return False, "Data must be a pandas DataFrame"
        
        if df.empty:
            return False, "DataFrame is empty"
        
        if df.shape[0] == 0:
            return False, "DataFrame has no rows"
        
        if df.shape[1] == 0:
            return False, "DataFrame has no columns"
        
        # Check for reasonable size limits
        if df.shape[0] > 1000000:  # 1M rows
            return False, "DataFrame too large (>1M rows). Please use a smaller dataset."
        
        if df.shape[1] > 1000:  # 1000 columns
            return False, "DataFrame has too many columns (>1000). Please reduce the number of columns."
        
        # Check for all-null columns
        all_null_cols = df.columns[df.isnull().all()].tolist()
        if all_null_cols:
            return False, f"Columns with all null values found: {', '.join(all_null_cols)}"
        
        return True, "DataFrame validation passed"
    
    def validate_analysis_prompt(self, prompt: str) -> Tuple[bool, str]:
        """Validate analysis prompt."""
        if not prompt:
            return False, "Analysis prompt is required"
        
        if not isinstance(prompt, str):
            return False, "Prompt must be a string"
        
        prompt = prompt.strip()
        
        if len(prompt) == 0:
            return False, "Prompt cannot be empty"
        
        if len(prompt) < 5:
            return False, "Prompt too short. Please provide more details."
        
        if len(prompt) > 1000:
            return False, "Prompt too long. Please keep it under 1000 characters."
        
        return True, "Prompt validation passed"
    
    def validate_api_response(self, response: requests.Response) -> Tuple[bool, str]:
        """Validate API response."""
        if response is None:
            return False, "No response received"
        
        # Check status code
        if response.status_code == 401:
            return False, "Authentication failed. Please check your API key."
        
        if response.status_code == 403:
            return False, "Access forbidden. Please check your API permissions."
        
        if response.status_code == 404:
            return False, "API endpoint not found."
        
        if response.status_code == 429:
            return False, "Rate limit exceeded. Please try again later."
        
        if response.status_code >= 500:
            return False, f"Server error ({response.status_code}). Please try again later."
        
        if response.status_code != 200:
            return False, f"API request failed with status {response.status_code}"
        
        # Check content type
        content_type = response.headers.get('content-type', '').lower()
        if 'application/json' not in content_type:
            return False, "Invalid response format. Expected JSON."
        
        # Try to parse JSON
        try:
            response.json()
        except ValueError:
            return False, "Invalid JSON response"
        
        return True, "API response validation passed"
    
    def sanitize_input(self, text: str, max_length: int = 1000) -> str:
        """Sanitize user input."""
        if not isinstance(text, str):
            text = str(text)
        
        # Remove potentially dangerous characters
        text = re.sub(r'[<>"\']', '', text)
        
        # Limit length
        if len(text) > max_length:
            text = text[:max_length]
        
        # Strip whitespace
        text = text.strip()
        
        return text
    
    def create_user_friendly_error(self, error_message: str) -> str:
        """Convert technical error messages to user-friendly ones."""
        error_mappings = {
            'connection': 'Unable to connect to the service. Please check your internet connection.',
            'timeout': 'The request took too long. Please try again.',
            'authentication': 'Authentication failed. Please check your API key.',
            'permission': 'You do not have permission to perform this action.',
            'not found': 'The requested resource was not found.',
            'rate limit': 'Too many requests. Please wait a moment and try again.',
            'server error': 'The server is experiencing issues. Please try again later.',
            'invalid format': 'The file format is not supported. Please use CSV, Excel, or JSON files.',
            'file too large': 'The file is too large. Please use a file smaller than 100MB.',
            'empty file': 'The file appears to be empty. Please check your file.',
            'parsing error': 'Unable to read the file. Please check the file format.',
        }
        
        error_lower = error_message.lower()
        
        for key, friendly_message in error_mappings.items():
            if key in error_lower:
                return friendly_message
        
        # If no specific mapping found, return a generic message
        return "An error occurred while processing your request. Please try again or contact support if the problem persists."
    
    def log_user_action(self, action: str, user_id: str = "anonymous", 
                       details: Dict[str, Any] = None):
        """Log user actions for debugging and analytics."""
        log_data = {
            'action': action,
            'user_id': user_id,
            'timestamp': pd.Timestamp.now().isoformat(),
            'details': details or {}
        }
        
        logger.info(f"User action: {log_data}")
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error response."""
        return {
            'success': False,
            'error': error_message,
            'user_message': self.create_user_friendly_error(error_message),
            'timestamp': pd.Timestamp.now().isoformat()
        }

# Global error handler instance
error_handler = ErrorHandler()
