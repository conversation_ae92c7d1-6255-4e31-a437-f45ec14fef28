"""
AiBiao API结果展示组件
专门用于将AiBiao API返回的结果展示成现代化界面
"""

import gradio as gr
import json
from typing import Dict, List, Optional, Any, Tuple
import re

class AiBiaoDisplayManager:
    """管理AiBiao API结果的展示"""
    
    def __init__(self):
        self.chart_counter = 0
        
    def format_chat_analysis_result(self, response_data: Dict[str, Any], chat_id: str = "") -> str:
        """
        格式化聊天分析结果，模拟截图中的现代化界面样式
        """
        if not response_data:
            return self._create_error_card("No analysis result received")
        
        result_text = response_data.get("result", "")
        steps = response_data.get("steps", [])
        questions = response_data.get("questions", "")
        title = response_data.get("title", "AI数据分析")
        
        # 解析建议问题
        suggested_questions = []
        if questions:
            try:
                if isinstance(questions, str):
                    suggested_questions = json.loads(questions)
                else:
                    suggested_questions = questions
            except:
                pass
        
        # 创建现代化的结果展示卡片
        html = f"""
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; padding: 2rem; margin: 1rem 0; color: white;">
            <h2 style="margin: 0 0 1rem 0; font-size: 1.5rem; font-weight: 600;">🤖 AI分析完成</h2>
            <p style="margin: 0; opacity: 0.9; font-size: 1rem;">{title}</p>
        </div>
        
        <div style="background: white; border-radius: 12px; padding: 2rem; margin: 1rem 0; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <h3 style="color: #2d3748; margin: 0 0 1rem 0; font-size: 1.2rem; font-weight: 600;">📊 分析结果</h3>
            <div style="background: #f7fafc; border-radius: 8px; padding: 1.5rem; border-left: 4px solid #667eea;">
                <p style="margin: 0; line-height: 1.6; color: #4a5568;">{result_text}</p>
            </div>
        </div>
        """
        
        # 添加处理步骤
        if steps:
            html += """
            <div style="background: white; border-radius: 12px; padding: 2rem; margin: 1rem 0; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <h3 style="color: #2d3748; margin: 0 0 1rem 0; font-size: 1.2rem; font-weight: 600;">⚙️ 处理步骤</h3>
                <div style="space-y: 0.5rem;">
            """
            
            for i, step in enumerate(steps):
                status = step.get("status", 0)
                content = step.get("content", "")
                
                if status == 2:  # 完成
                    icon = "✅"
                    color = "#48bb78"
                elif status == 1:  # 进行中
                    icon = "⏳"
                    color = "#ed8936"
                else:  # 未开始或失败
                    icon = "⭕"
                    color = "#e53e3e"
                
                html += f"""
                    <div style="display: flex; align-items: center; padding: 0.75rem; background: #f7fafc; border-radius: 6px; margin-bottom: 0.5rem;">
                        <span style="margin-right: 0.75rem; font-size: 1.2rem;">{icon}</span>
                        <span style="color: {color}; font-weight: 500;">{content}</span>
                    </div>
                """
            
            html += "</div></div>"
        
        # 添加建议问题
        if suggested_questions:
            html += """
            <div style="background: white; border-radius: 12px; padding: 2rem; margin: 1rem 0; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                <h3 style="color: #2d3748; margin: 0 0 1rem 0; font-size: 1.2rem; font-weight: 600;">💡 建议的后续问题</h3>
                <div style="display: grid; gap: 0.75rem;">
            """
            
            for question in suggested_questions[:5]:  # 最多显示5个问题
                html += f"""
                    <div style="background: #edf2f7; border-radius: 8px; padding: 1rem; border: 1px solid #e2e8f0; cursor: pointer; transition: all 0.2s;" 
                         onmouseover="this.style.backgroundColor='#e2e8f0'; this.style.borderColor='#667eea';" 
                         onmouseout="this.style.backgroundColor='#edf2f7'; this.style.borderColor='#e2e8f0';">
                        <span style="color: #4a5568; font-size: 0.95rem;">❓ {question}</span>
                    </div>
                """
            
            html += "</div></div>"
        
        # 添加会话信息
        if chat_id:
            html += f"""
            <div style="background: #f7fafc; border-radius: 8px; padding: 1rem; margin: 1rem 0; border: 1px solid #e2e8f0;">
                <p style="margin: 0; color: #718096; font-size: 0.9rem;">
                    💬 会话ID: <code style="background: #edf2f7; padding: 0.25rem 0.5rem; border-radius: 4px; font-family: monospace;">{chat_id[:8]}...</code>
                </p>
            </div>
            """
        
        return html
    
    def format_chart_result(self, chart_data: Dict[str, Any]) -> Tuple[str, str]:
        """
        格式化图表结果，返回状态HTML和图表iframe
        """
        if not chart_data:
            return self._create_error_card("No chart data received"), ""
        
        chart_id = chart_data.get("chartId", "")
        title = chart_data.get("title", "AI生成图表")
        chart_url = chart_data.get("chartUrl", "")
        share_url = chart_data.get("shareUrl", "")
        
        # 状态信息卡片
        status_html = f"""
        <div style="background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); border-radius: 12px; padding: 2rem; margin: 1rem 0; color: white;">
            <h2 style="margin: 0 0 1rem 0; font-size: 1.5rem; font-weight: 600;">📈 图表生成成功</h2>
            <p style="margin: 0; opacity: 0.9; font-size: 1rem;">{title}</p>
        </div>
        
        <div style="background: white; border-radius: 12px; padding: 2rem; margin: 1rem 0; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <h3 style="color: #2d3748; margin: 0 0 1rem 0; font-size: 1.2rem; font-weight: 600;">🔗 图表链接</h3>
            <div style="display: grid; gap: 1rem;">
                <div style="background: #f7fafc; border-radius: 8px; padding: 1rem; border: 1px solid #e2e8f0;">
                    <p style="margin: 0 0 0.5rem 0; color: #4a5568; font-weight: 500;">📊 编辑链接（需要登录）:</p>
                    <a href="{chart_url}" target="_blank" style="color: #667eea; text-decoration: none; font-family: monospace; font-size: 0.9rem; word-break: break-all;">{chart_url}</a>
                </div>
                <div style="background: #f7fafc; border-radius: 8px; padding: 1rem; border: 1px solid #e2e8f0;">
                    <p style="margin: 0 0 0.5rem 0; color: #4a5568; font-weight: 500;">🔗 分享链接（免登录查看）:</p>
                    <a href="{share_url}" target="_blank" style="color: #667eea; text-decoration: none; font-family: monospace; font-size: 0.9rem; word-break: break-all;">{share_url}</a>
                </div>
            </div>
        </div>
        
        <div style="background: #f7fafc; border-radius: 8px; padding: 1rem; margin: 1rem 0; border: 1px solid #e2e8f0;">
            <p style="margin: 0; color: #718096; font-size: 0.9rem;">
                📋 图表ID: <code style="background: #edf2f7; padding: 0.25rem 0.5rem; border-radius: 4px; font-family: monospace;">{chart_id[:8]}...</code>
            </p>
        </div>
        """
        
        # 图表iframe
        chart_iframe = f"""
        <div style="background: white; border-radius: 12px; padding: 1rem; margin: 1rem 0; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <h3 style="color: #2d3748; margin: 0 0 1rem 0; font-size: 1.2rem; font-weight: 600;">📊 图表预览</h3>
            <div style="border: 1px solid #e2e8f0; border-radius: 8px; overflow: hidden;">
                <iframe src="{share_url}" width="100%" height="500" frameborder="0" style="border-radius: 8px;"></iframe>
            </div>
        </div>
        """ if share_url else ""
        
        return status_html, chart_iframe
    
    def format_workflow_execution_result(self, execution_data: Dict[str, Any]) -> str:
        """
        格式化工作流执行结果
        """
        if not execution_data:
            return self._create_error_card("No execution data received")
        
        execution_id = execution_data.get("executionId", "")
        status = execution_data.get("status", "unknown")
        started_at = execution_data.get("startedAt", "")
        estimated_duration = execution_data.get("estimatedDuration", "")
        
        # 状态颜色映射
        status_colors = {
            "running": "#ed8936",
            "completed": "#48bb78",
            "failed": "#e53e3e",
            "waiting": "#667eea"
        }
        
        status_color = status_colors.get(status, "#718096")
        
        html = f"""
        <div style="background: linear-gradient(135deg, {status_color} 0%, {status_color}dd 100%); border-radius: 12px; padding: 2rem; margin: 1rem 0; color: white;">
            <h2 style="margin: 0 0 1rem 0; font-size: 1.5rem; font-weight: 600;">⚙️ 工作流执行中</h2>
            <p style="margin: 0; opacity: 0.9; font-size: 1rem;">状态: {status.upper()}</p>
        </div>
        
        <div style="background: white; border-radius: 12px; padding: 2rem; margin: 1rem 0; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <h3 style="color: #2d3748; margin: 0 0 1rem 0; font-size: 1.2rem; font-weight: 600;">📋 执行详情</h3>
            <div style="display: grid; gap: 1rem;">
                <div style="background: #f7fafc; border-radius: 8px; padding: 1rem; border-left: 4px solid #667eea;">
                    <p style="margin: 0; color: #4a5568;"><strong>执行ID:</strong> {execution_id}</p>
                </div>
                <div style="background: #f7fafc; border-radius: 8px; padding: 1rem; border-left: 4px solid #48bb78;">
                    <p style="margin: 0; color: #4a5568;"><strong>开始时间:</strong> {started_at}</p>
                </div>
                <div style="background: #f7fafc; border-radius: 8px; padding: 1rem; border-left: 4px solid #ed8936;">
                    <p style="margin: 0; color: #4a5568;"><strong>预计耗时:</strong> {estimated_duration}</p>
                </div>
            </div>
        </div>
        """
        
        return html
    
    def _create_error_card(self, error_message: str) -> str:
        """创建错误信息卡片"""
        return f"""
        <div style="background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%); border-radius: 12px; padding: 2rem; margin: 1rem 0; color: white;">
            <h2 style="margin: 0 0 1rem 0; font-size: 1.5rem; font-weight: 600;">❌ 处理失败</h2>
            <p style="margin: 0; opacity: 0.9; font-size: 1rem;">{error_message}</p>
        </div>
        """
    
    def create_loading_card(self, message: str = "正在处理中...") -> str:
        """创建加载中的卡片"""
        return f"""
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; padding: 2rem; margin: 1rem 0; color: white;">
            <h2 style="margin: 0 0 1rem 0; font-size: 1.5rem; font-weight: 600;">⏳ {message}</h2>
            <div style="display: flex; align-items: center; gap: 1rem;">
                <div style="width: 20px; height: 20px; border: 2px solid rgba(255,255,255,0.3); border-top: 2px solid white; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                <p style="margin: 0; opacity: 0.9; font-size: 1rem;">请稍候，AI正在分析您的数据...</p>
            </div>
        </div>
        
        <style>
        @keyframes spin {{
            0% {{ transform: rotate(0deg); }}
            100% {{ transform: rotate(360deg); }}
        }}
        </style>
        """
