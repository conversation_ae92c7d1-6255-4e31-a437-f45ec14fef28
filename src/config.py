"""
Configuration management for the AiBiao AI Data Analysis System.
"""

import os
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for the application."""
    
    def __init__(self):
        self.api_base_url = "https://aibiao.cn/api/enterprise/external"
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        self.supported_file_types = [".xlsx", ".xls", ".csv", ".json"]
        self.default_theme = "soft"
        self.app_title = "AiBiao AI Data Analysis System"
        self.app_description = "Modern AI-powered data analysis with intelligent chart generation"
        
    def get_api_key(self) -> str:
        """Get the API key from environment variables."""
        return os.getenv("AIBIAO_API_KEY", "")
    
    def set_api_key(self, api_key: str) -> None:
        """Set the API key in environment variables."""
        os.environ["AIBIAO_API_KEY"] = api_key

def load_config() -> Config:
    """Load and return the application configuration."""
    return Config()

# Global configuration instance
config = load_config()
