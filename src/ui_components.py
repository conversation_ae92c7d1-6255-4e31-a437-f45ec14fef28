"""
UI Components for the AiBiao AI Data Analysis System.
"""

import gradio as gr
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, List, Optional, Any, Tuple
import os
import tempfile
from pathlib import Path

from aibiao_client import AiBiaoClient, APIResponse
from data_processor import DataProcessor
from visualization import ChartGenerator
from aibiao_display import AiBiaoDisplayManager

def create_main_interface(client: AiBiaoClient, config) -> gr.Blocks:
    """Create the main Gradio interface."""
    
    # Custom CSS for modern theme
    custom_css = """
    .gradio-container {
        max-width: 1200px !important;
        margin: auto !important;
    }
    .main-header {
        text-align: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    .feature-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
    }
    .status-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 8px;
    }
    .status-connected {
        background-color: #10b981;
    }
    .status-disconnected {
        background-color: #ef4444;
    }
    .tab-nav {
        background: #f8fafc;
        border-radius: 8px;
        padding: 0.5rem;
    }
    """
    
    with gr.Blocks(
        theme=gr.themes.Soft(
            primary_hue="blue",
            secondary_hue="purple",
            neutral_hue="slate"
        ),
        css=custom_css,
        title=config.app_title
    ) as app:
        
        # Header
        with gr.Row():
            gr.HTML(f"""
            <div class="main-header">
                <h1>🤖 {config.app_title}</h1>
                <p>{config.app_description}</p>
            </div>
            """)
        
        # API Key Configuration Section
        with gr.Row():
            with gr.Column(scale=1):
                gr.HTML("<h3>🔑 API Configuration</h3>")
                api_key_input = gr.Textbox(
                    label="AiBiao API Key",
                    placeholder="Enter your AiBiao API key (sk-...)",
                    type="password",
                    value=config.get_api_key()
                )
                api_status = gr.HTML("<span class='status-indicator status-disconnected'></span>Not Connected")
                
                with gr.Row():
                    connect_btn = gr.Button("Connect", variant="primary", size="sm")
                    test_btn = gr.Button("Test Connection", variant="secondary", size="sm")
        
        # Main Application Tabs
        with gr.Tabs(elem_classes="tab-nav") as main_tabs:
            
            # Data Analysis Tab
            with gr.Tab("📊 Data Analysis", id="analysis"):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.HTML("<h3>Data Input</h3>")
                        
                        # Data input methods
                        input_method = gr.Radio(
                            choices=["File Upload", "URL Import", "Direct Input"],
                            value="File Upload",
                            label="Input Method"
                        )
                        
                        # File upload
                        file_upload = gr.File(
                            label="Upload Data File",
                            file_types=[".xlsx", ".xls", ".csv", ".json"],
                            visible=True
                        )
                        
                        # URL input
                        url_input = gr.Textbox(
                            label="File URL",
                            placeholder="https://example.com/data.xlsx",
                            visible=False
                        )
                        
                        # Direct data input
                        direct_input = gr.Textbox(
                            label="Paste CSV Data",
                            placeholder="Column1,Column2,Column3\nValue1,Value2,Value3",
                            lines=10,
                            visible=False
                        )
                        
                        # Analysis prompt
                        analysis_prompt = gr.Textbox(
                            label="Analysis Request",
                            placeholder="What would you like to analyze? (e.g., 'Create a sales trend chart', 'Analyze customer demographics')",
                            lines=3
                        )
                        
                        analyze_btn = gr.Button("🚀 Start Analysis", variant="primary", size="lg")
                    
                    with gr.Column(scale=2):
                        gr.HTML("<h3>Analysis Results</h3>")
                        
                        # Results display
                        analysis_status = gr.HTML("Ready to analyze data...")
                        analysis_output = gr.HTML("")
                        
                        # Chart display
                        chart_display = gr.Plot(label="Generated Chart")
                        
                        # Data preview
                        data_preview = gr.Dataframe(
                            label="Data Preview",
                            interactive=False
                        )
                        
                        # Export options
                        with gr.Row():
                            export_chart_btn = gr.Button("📥 Export Chart", size="sm")
                            export_data_btn = gr.Button("📥 Export Data", size="sm")
            
            # Chat Management Tab
            with gr.Tab("💬 Chat Sessions", id="chats"):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.HTML("<h3>Chat Sessions</h3>")
                        
                        # Chat list controls
                        with gr.Row():
                            refresh_chats_btn = gr.Button("🔄 Refresh", size="sm")
                            search_chats = gr.Textbox(
                                placeholder="Search chats...",
                                scale=3
                            )
                        
                        # Chat list
                        chat_list = gr.Dataframe(
                            headers=["ID", "Title", "Created", "Messages"],
                            datatype=["str", "str", "str", "number"],
                            interactive=False,
                            label="Your Chat Sessions"
                        )
                        
                        # Chat actions
                        with gr.Row():
                            view_chat_btn = gr.Button("👁️ View", size="sm")
                            delete_chat_btn = gr.Button("🗑️ Delete", size="sm", variant="stop")
                    
                    with gr.Column(scale=2):
                        gr.HTML("<h3>Chat Details</h3>")
                        
                        # Selected chat info
                        selected_chat_info = gr.HTML("Select a chat to view details")
                        
                        # Chat messages
                        chat_messages = gr.Chatbot(
                            label="Chat History",
                            height=400
                        )
                        
                        # New message input
                        new_message = gr.Textbox(
                            label="Send Message",
                            placeholder="Ask a follow-up question...",
                            lines=2
                        )
                        send_message_btn = gr.Button("📤 Send", variant="primary")
            
            # Charts Gallery Tab
            with gr.Tab("📈 Charts Gallery", id="charts"):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.HTML("<h3>Your Charts</h3>")
                        
                        # Chart list controls
                        with gr.Row():
                            refresh_charts_btn = gr.Button("🔄 Refresh", size="sm")
                            search_charts = gr.Textbox(
                                placeholder="Search charts...",
                                scale=3
                            )
                        
                        # Chart list
                        chart_list = gr.Dataframe(
                            headers=["ID", "Title", "Type", "Created"],
                            datatype=["str", "str", "str", "str"],
                            interactive=False,
                            label="Your Charts"
                        )
                        
                        # Chart actions
                        with gr.Row():
                            view_chart_btn = gr.Button("👁️ View", size="sm")
                            share_chart_btn = gr.Button("🔗 Share", size="sm")
                            delete_chart_btn = gr.Button("🗑️ Delete", size="sm", variant="stop")
                    
                    with gr.Column(scale=2):
                        gr.HTML("<h3>Chart Preview</h3>")
                        
                        # Selected chart info
                        selected_chart_info = gr.HTML("Select a chart to preview")
                        
                        # Chart preview
                        chart_preview = gr.HTML(
                            label="Chart Preview",
                            value="<div style='text-align: center; padding: 2rem; color: #666;'>No chart selected</div>"
                        )
                        
                        # Chart links
                        chart_links = gr.HTML("")
            
            # Workflows Tab
            with gr.Tab("⚙️ Workflows", id="workflows"):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.HTML("<h3>Saved Workflows</h3>")
                        
                        # Workflow list controls
                        with gr.Row():
                            refresh_workflows_btn = gr.Button("🔄 Refresh", size="sm")
                            search_workflows = gr.Textbox(
                                placeholder="Search workflows...",
                                scale=3
                            )
                        
                        # Workflow list
                        workflow_list = gr.Dataframe(
                            headers=["ID", "Name", "Description", "Created"],
                            datatype=["str", "str", "str", "str"],
                            interactive=False,
                            label="Your Workflows"
                        )
                        
                        # Workflow actions
                        with gr.Row():
                            execute_workflow_btn = gr.Button("▶️ Execute", size="sm", variant="primary")
                            edit_workflow_btn = gr.Button("✏️ Edit", size="sm")
                            delete_workflow_btn = gr.Button("🗑️ Delete", size="sm", variant="stop")
                    
                    with gr.Column(scale=2):
                        gr.HTML("<h3>Workflow Execution</h3>")
                        
                        # Selected workflow info
                        selected_workflow_info = gr.HTML("Select a workflow to execute")
                        
                        # Workflow input files
                        workflow_files = gr.File(
                            label="Input Files for Workflow",
                            file_count="multiple",
                            file_types=[".xlsx", ".xls", ".csv", ".json"]
                        )
                        
                        # Execution controls
                        workflow_title = gr.Textbox(
                            label="Execution Title (Optional)",
                            placeholder="My workflow execution"
                        )
                        
                        execute_btn = gr.Button("🚀 Execute Workflow", variant="primary", size="lg")
                        
                        # Execution status
                        execution_status = gr.HTML("Ready to execute workflow")
                        execution_results = gr.HTML("")
        
        # Initialize components
        data_processor = DataProcessor()
        chart_generator = ChartGenerator()
        display_manager = AiBiaoDisplayManager()

        # State variables
        current_data = gr.State(None)
        current_chat_id = gr.State(None)
        selected_chart_id = gr.State(None)
        selected_workflow_id = gr.State(None)

        # Event Handlers

        def handle_api_key_connect(api_key):
            """Handle API key connection."""
            if not api_key:
                return "<span class='status-indicator status-disconnected'></span>API key required"

            client.set_api_key(api_key)
            config.set_api_key(api_key)

            # Test the connection
            response = client.check_api_key_status()
            if response.success:
                status_html = f"<span class='status-indicator status-connected'></span>Connected - {response.data.get('usedQuota', 0)}/{response.data.get('totalQuota', 0)} quota used"
                return status_html
            else:
                return f"<span class='status-indicator status-disconnected'></span>Connection failed: {response.error}"

        def handle_test_connection(api_key):
            """Test API connection."""
            if not api_key:
                return "Please enter an API key first"

            client.set_api_key(api_key)
            response = client.check_api_key_status()

            if response.success:
                data = response.data
                return f"✅ Connection successful!\nAPI Key: {data.get('apiKey', 'N/A')}\nQuota: {data.get('usedQuota', 0)}/{data.get('totalQuota', 0)}\nStatus: {'Active' if data.get('isActive') else 'Inactive'}"
            else:
                return f"❌ Connection failed: {response.error}"

        def handle_input_method_change(method):
            """Handle input method selection change."""
            return (
                gr.update(visible=(method == "File Upload")),
                gr.update(visible=(method == "URL Import")),
                gr.update(visible=(method == "Direct Input"))
            )

        def handle_data_analysis(input_method, file_upload, url_input, direct_input, analysis_prompt):
            """Handle data analysis request using AiBiao API."""
            try:
                # Prepare file URL for AiBiao API
                file_url = None

                if input_method == "File Upload":
                    if not file_upload:
                        return "Please upload a file", None, None, None
                    # Upload file to temporary storage and get URL
                    success, file_url = data_processor.upload_file_to_temp_url(file_upload.name)
                    if not success:
                        return f"Error uploading file: {file_url}", None, None, None

                elif input_method == "URL Import":
                    if not url_input:
                        return "Please enter a URL", None, None, None
                    file_url = url_input

                elif input_method == "Direct Input":
                    if not direct_input:
                        return "Please enter data", None, None, None
                    # Save direct input as temporary file and upload
                    success, temp_file = data_processor.save_direct_input_as_file(direct_input)
                    if not success:
                        return f"Error processing input: {temp_file}", None, None, None
                    success, file_url = data_processor.upload_file_to_temp_url(temp_file)
                    if not success:
                        return f"Error uploading data: {file_url}", None, None, None
                else:
                    return "Invalid input method", None, None, None

                if not analysis_prompt:
                    analysis_prompt = "请分析这个数据并生成相应的图表"

                # Show loading state
                loading_html = display_manager.create_loading_card("正在调用AiBiao AI进行数据分析...")

                # Call AiBiao API for analysis
                if analysis_prompt and ("图表" in analysis_prompt or "chart" in analysis_prompt.lower() or "可视化" in analysis_prompt):
                    # Use chart creation API
                    response = client.create_new_chart(file_url, analysis_prompt)
                    if response.success:
                        chart_data = response.data

                        # Use display manager to format the result
                        status_html, chart_iframe = display_manager.format_chart_result(chart_data)

                        # Combine status and chart display
                        combined_html = status_html + chart_iframe

                        return combined_html, None, None, chart_data
                    else:
                        error_html = display_manager._create_error_card(f"图表生成失败: {response.error}")
                        return error_html, None, None, None
                else:
                    # Use chat analysis API
                    response = client.create_new_chat([file_url], f"数据分析请求: {analysis_prompt}")
                    if response.success:
                        chat_data = response.data

                        # Send analysis request
                        chat_id = chat_data.get("chatId") if chat_data else None
                        if chat_id:
                            analysis_response = client.append_chat_message(chat_id, analysis_prompt)
                            if analysis_response.success:
                                result_data = analysis_response.data

                                # Use display manager to format the result
                                status_html = display_manager.format_chat_analysis_result(result_data, chat_id)

                                return status_html, None, None, result_data
                            else:
                                error_html = display_manager._create_error_card(f"分析失败: {analysis_response.error}")
                                return error_html, None, None, None
                        else:
                            error_html = display_manager._create_error_card("无法创建分析会话")
                            return error_html, None, None, None
                    else:
                        error_html = display_manager._create_error_card(f"分析失败: {response.error}")
                        return error_html, None, None, None

            except Exception as e:
                return f"Error during analysis: {str(e)}", None, None, None

        def refresh_chat_sessions():
            """Refresh the chat sessions list with modern display."""
            try:
                response = client.list_chats()
                if response.success and response.data:
                    chats = response.data.get("chats", [])
                    if chats:
                        # Format for dataframe display
                        chat_data = []
                        for chat in chats:
                            chat_id = chat.get("chatId", "")
                            title = chat.get("title", f"会话 {chat_id[:8]}")
                            created_at = chat.get("createdAt", "")
                            message_count = chat.get("messageCount", 0)
                            chat_data.append([chat_id[:12] + "...", title, created_at, message_count])

                        return chat_data
                    else:
                        return []
                else:
                    return []
            except Exception as e:
                return []

        def view_chat_details(chat_list_data, evt: gr.SelectData):
            """View details of selected chat."""
            try:
                if not chat_list_data or evt.index[0] >= len(chat_list_data):
                    return "No chat selected", []

                # Get full chat ID (this is simplified - in real implementation you'd store full IDs)
                selected_row = chat_list_data[evt.index[0]]
                chat_title = selected_row[1]

                # Create info display
                info_html = f"""
                <div style="background: white; border-radius: 12px; padding: 1.5rem; margin: 1rem 0; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                    <h4 style="color: #2d3748; margin: 0 0 1rem 0;">💬 {chat_title}</h4>
                    <p style="margin: 0; color: #718096;">选择此会话以查看详细信息和历史消息</p>
                </div>
                """

                # Simulate chat messages
                messages = [
                    ("用户", "请分析这个销售数据"),
                    ("AI助手", "我已经分析了您的销售数据，发现以下几个关键趋势：\n\n1. 第三季度销售额增长了25%\n2. 产品A的表现最佳\n3. 建议加大对产品A的推广力度")
                ]

                return info_html, messages

            except Exception as e:
                error_html = display_manager._create_error_card(f"查看聊天详情时出错: {str(e)}")
                return error_html, []

        # Connect event handlers
        connect_btn.click(
            handle_api_key_connect,
            inputs=[api_key_input],
            outputs=[api_status]
        )

        test_btn.click(
            handle_test_connection,
            inputs=[api_key_input],
            outputs=[gr.Textbox(label="Connection Test Result", lines=5, visible=True)]
        )

        input_method.change(
            handle_input_method_change,
            inputs=[input_method],
            outputs=[file_upload, url_input, direct_input]
        )

        analyze_btn.click(
            handle_data_analysis,
            inputs=[input_method, file_upload, url_input, direct_input, analysis_prompt],
            outputs=[analysis_status, chart_display, data_preview, current_data]
        )

        # Chat session event handlers
        refresh_chats_btn.click(
            refresh_chat_sessions,
            outputs=[chat_list]
        )

        chat_list.select(
            view_chat_details,
            inputs=[chat_list],
            outputs=[selected_chat_info, chat_messages]
        )

        return app
