"""
Visualization utilities for the AiBiao AI Data Analysis System.
"""

import plotly.express as px
import plotly.graph_objects as go
import plotly.figure_factory as ff
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import seaborn as sns
import matplotlib.pyplot as plt
import io
import base64

class ChartGenerator:
    """Generates various types of charts and visualizations."""
    
    def __init__(self):
        self.color_palette = px.colors.qualitative.Set3
        self.theme = "plotly_white"
    
    def create_basic_chart(self, df: pd.DataFrame, chart_type: str, 
                          x_col: str = None, y_col: str = None, 
                          color_col: str = None, **kwargs) -> go.Figure:
        """
        Create a basic chart based on the specified type.
        
        Args:
            df: DataFrame containing the data
            chart_type: Type of chart ('line', 'bar', 'scatter', 'pie', etc.)
            x_col: Column for x-axis
            y_col: Column for y-axis
            color_col: Column for color grouping
            **kwargs: Additional arguments for the chart
            
        Returns:
            Plotly figure object
        """
        try:
            fig = None
            
            if chart_type == 'line':
                fig = px.line(df, x=x_col, y=y_col, color=color_col, 
                             template=self.theme, **kwargs)
            
            elif chart_type == 'bar':
                fig = px.bar(df, x=x_col, y=y_col, color=color_col,
                            template=self.theme, **kwargs)
            
            elif chart_type == 'scatter':
                fig = px.scatter(df, x=x_col, y=y_col, color=color_col,
                               template=self.theme, **kwargs)
            
            elif chart_type == 'pie':
                fig = px.pie(df, values=y_col, names=x_col,
                            template=self.theme, **kwargs)
            
            elif chart_type == 'histogram':
                fig = px.histogram(df, x=x_col, color=color_col,
                                 template=self.theme, **kwargs)
            
            elif chart_type == 'box':
                fig = px.box(df, x=x_col, y=y_col, color=color_col,
                           template=self.theme, **kwargs)
            
            elif chart_type == 'violin':
                fig = px.violin(df, x=x_col, y=y_col, color=color_col,
                              template=self.theme, **kwargs)
            
            elif chart_type == 'heatmap':
                # For heatmap, we need a correlation matrix or pivot table
                if df.select_dtypes(include=[np.number]).shape[1] > 1:
                    corr_matrix = df.select_dtypes(include=[np.number]).corr()
                    fig = px.imshow(corr_matrix, text_auto=True, aspect="auto",
                                  template=self.theme, **kwargs)
                else:
                    raise ValueError("Heatmap requires multiple numeric columns")
            
            else:
                raise ValueError(f"Unsupported chart type: {chart_type}")
            
            # Apply common styling
            if fig:
                fig.update_layout(
                    font=dict(family="Arial, sans-serif", size=12),
                    plot_bgcolor='rgba(0,0,0,0)',
                    paper_bgcolor='rgba(0,0,0,0)',
                    margin=dict(l=40, r=40, t=60, b=40)
                )
            
            return fig
            
        except Exception as e:
            # Return an error figure
            fig = go.Figure()
            fig.add_annotation(
                text=f"Error creating chart: {str(e)}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False,
                font=dict(size=16, color="red")
            )
            return fig
    
    def create_dashboard_charts(self, df: pd.DataFrame) -> List[go.Figure]:
        """
        Create a set of dashboard charts for data exploration.
        
        Args:
            df: DataFrame to analyze
            
        Returns:
            List of Plotly figures
        """
        charts = []
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
        
        try:
            # 1. Data overview chart
            if len(numeric_cols) > 0:
                # Distribution of numeric columns
                fig = make_subplots(
                    rows=min(2, len(numeric_cols)),
                    cols=min(2, (len(numeric_cols) + 1) // 2),
                    subplot_titles=numeric_cols[:4]
                )
                
                for i, col in enumerate(numeric_cols[:4]):
                    row = (i // 2) + 1
                    col_pos = (i % 2) + 1
                    fig.add_trace(
                        go.Histogram(x=df[col], name=col, showlegend=False),
                        row=row, col=col_pos
                    )
                
                fig.update_layout(
                    title="Numeric Columns Distribution",
                    template=self.theme,
                    height=400
                )
                charts.append(fig)
            
            # 2. Correlation heatmap
            if len(numeric_cols) > 1:
                corr_matrix = df[numeric_cols].corr()
                fig = px.imshow(
                    corr_matrix,
                    text_auto=True,
                    aspect="auto",
                    title="Correlation Matrix",
                    template=self.theme
                )
                charts.append(fig)
            
            # 3. Categorical data analysis
            if len(categorical_cols) > 0:
                for col in categorical_cols[:2]:  # Limit to first 2 categorical columns
                    value_counts = df[col].value_counts().head(10)
                    fig = px.bar(
                        x=value_counts.index,
                        y=value_counts.values,
                        title=f"Distribution of {col}",
                        template=self.theme
                    )
                    fig.update_xaxes(title=col)
                    fig.update_yaxes(title="Count")
                    charts.append(fig)
            
            # 4. Time series if date column exists
            date_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
            if len(date_cols) > 0 and len(numeric_cols) > 0:
                date_col = date_cols[0]
                numeric_col = numeric_cols[0]
                fig = px.line(
                    df.sort_values(date_col),
                    x=date_col,
                    y=numeric_col,
                    title=f"{numeric_col} over Time",
                    template=self.theme
                )
                charts.append(fig)
            
        except Exception as e:
            # Create error chart
            fig = go.Figure()
            fig.add_annotation(
                text=f"Error creating dashboard: {str(e)}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False,
                font=dict(size=16, color="red")
            )
            charts.append(fig)
        
        return charts
    
    def suggest_chart_types(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Suggest appropriate chart types based on data characteristics.
        
        Args:
            df: DataFrame to analyze
            
        Returns:
            List of chart suggestions with metadata
        """
        suggestions = []
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
        date_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
        
        # Line chart for time series
        if len(date_cols) > 0 and len(numeric_cols) > 0:
            suggestions.append({
                "type": "line",
                "title": "Time Series Analysis",
                "description": "Show trends over time",
                "x_col": date_cols[0],
                "y_col": numeric_cols[0],
                "suitable_for": "temporal data"
            })
        
        # Bar chart for categorical vs numeric
        if len(categorical_cols) > 0 and len(numeric_cols) > 0:
            suggestions.append({
                "type": "bar",
                "title": "Category Comparison",
                "description": "Compare values across categories",
                "x_col": categorical_cols[0],
                "y_col": numeric_cols[0],
                "suitable_for": "categorical comparison"
            })
        
        # Scatter plot for numeric vs numeric
        if len(numeric_cols) >= 2:
            suggestions.append({
                "type": "scatter",
                "title": "Correlation Analysis",
                "description": "Explore relationships between variables",
                "x_col": numeric_cols[0],
                "y_col": numeric_cols[1],
                "suitable_for": "correlation analysis"
            })
        
        # Pie chart for categorical distribution
        if len(categorical_cols) > 0:
            suggestions.append({
                "type": "pie",
                "title": "Distribution Analysis",
                "description": "Show proportional distribution",
                "x_col": categorical_cols[0],
                "y_col": None,
                "suitable_for": "proportion analysis"
            })
        
        # Histogram for distribution
        if len(numeric_cols) > 0:
            suggestions.append({
                "type": "histogram",
                "title": "Distribution Analysis",
                "description": "Show data distribution",
                "x_col": numeric_cols[0],
                "y_col": None,
                "suitable_for": "distribution analysis"
            })
        
        # Box plot for outlier detection
        if len(numeric_cols) > 0:
            suggestions.append({
                "type": "box",
                "title": "Outlier Detection",
                "description": "Identify outliers and quartiles",
                "x_col": categorical_cols[0] if categorical_cols else None,
                "y_col": numeric_cols[0],
                "suitable_for": "outlier detection"
            })
        
        return suggestions
    
    def export_chart(self, fig: go.Figure, format: str = 'png', 
                    width: int = 800, height: int = 600) -> Tuple[bool, str]:
        """
        Export a chart to specified format.
        
        Args:
            fig: Plotly figure to export
            format: Export format ('png', 'jpg', 'svg', 'pdf', 'html')
            width: Image width
            height: Image height
            
        Returns:
            Tuple of (success, file_path_or_error_message)
        """
        try:
            import tempfile
            
            with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{format}') as tmp_file:
                if format in ['png', 'jpg', 'jpeg']:
                    fig.write_image(tmp_file.name, format=format, width=width, height=height)
                elif format == 'svg':
                    fig.write_image(tmp_file.name, format='svg', width=width, height=height)
                elif format == 'pdf':
                    fig.write_image(tmp_file.name, format='pdf', width=width, height=height)
                elif format == 'html':
                    fig.write_html(tmp_file.name)
                else:
                    return False, f"Unsupported export format: {format}"
                
                return True, tmp_file.name
                
        except Exception as e:
            return False, f"Error exporting chart: {str(e)}"
