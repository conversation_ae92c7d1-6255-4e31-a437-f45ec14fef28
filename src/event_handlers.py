"""
Event handlers for the AiBiao AI Data Analysis System.
"""

import gradio as gr
import pandas as pd
import json
import tempfile
import os
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from aibiao_client import AiBiaoClient, APIResponse
from data_processor import DataProcessor
from visualization import ChartGenerator

class EventHandlers:
    """Handles all UI events and interactions."""
    
    def __init__(self, client: AiBiaoClient, data_processor: DataProcessor, 
                 chart_generator: ChartGenerator):
        self.client = client
        self.data_processor = data_processor
        self.chart_generator = chart_generator
    
    def handle_chat_analysis(self, file_upload, analysis_prompt):
        """Handle analysis using AiBiao chat API."""
        try:
            if not file_upload:
                return "Please upload a file first", None, None
            
            if not analysis_prompt:
                return "Please enter an analysis request", None, None
            
            # Upload file to temporary URL (in real implementation, use cloud storage)
            success, file_url = self.data_processor.upload_file_to_temp_url(file_upload.name)
            if not success:
                return f"Error uploading file: {file_url}", None, None
            
            # Create new chat with the file
            response = self.client.create_new_chat([file_url], f"Analysis: {analysis_prompt[:50]}...")
            
            if not response.success:
                return f"Error creating chat: {response.error}", None, None
            
            # Extract chat information from response
            chat_data = response.data
            chat_id = chat_data.get("chatId") if chat_data else None
            
            if not chat_id:
                return "Error: No chat ID received", None, None
            
            # Send analysis request
            analysis_response = self.client.append_chat_message(chat_id, analysis_prompt)
            
            if not analysis_response.success:
                return f"Error sending analysis request: {analysis_response.error}", None, None
            
            # Format the response
            result_data = analysis_response.data
            result_html = self._format_analysis_result(result_data)
            
            return result_html, chat_id, result_data
            
        except Exception as e:
            return f"Error during analysis: {str(e)}", None, None
    
    def handle_chart_creation(self, file_upload, chart_prompt):
        """Handle chart creation using AiBiao chart API."""
        try:
            if not file_upload:
                return "Please upload a file first", None
            
            if not chart_prompt:
                return "Please enter a chart description", None
            
            # Upload file to temporary URL
            success, file_url = self.data_processor.upload_file_to_temp_url(file_upload.name)
            if not success:
                return f"Error uploading file: {file_url}", None
            
            # Create chart using AiBiao API
            response = self.client.create_new_chart(file_url, chart_prompt)
            
            if not response.success:
                return f"Error creating chart: {response.error}", None
            
            chart_data = response.data
            chart_url = chart_data.get("shareUrl", "")
            chart_id = chart_data.get("chartId", "")
            
            # Create iframe to display the chart
            chart_html = f"""
            <div class="feature-card">
                <h4>📈 Chart Created Successfully</h4>
                <p><strong>Chart ID:</strong> {chart_id}</p>
                <p><strong>Title:</strong> {chart_data.get('title', 'Untitled')}</p>
                <div style="margin-top: 1rem;">
                    <iframe src="{chart_url}" width="100%" height="500" frameborder="0"></iframe>
                </div>
                <div style="margin-top: 1rem;">
                    <a href="{chart_url}" target="_blank" class="btn btn-primary">Open in New Tab</a>
                    <a href="{chart_data.get('chartUrl', '')}" target="_blank" class="btn btn-secondary">Edit Chart</a>
                </div>
            </div>
            """
            
            return chart_html, chart_data
            
        except Exception as e:
            return f"Error creating chart: {str(e)}", None
    
    def handle_chat_list_refresh(self, search_term=""):
        """Refresh the chat list."""
        try:
            response = self.client.list_chats(
                page=1, 
                limit=50, 
                include_messages=True, 
                search=search_term
            )
            
            if not response.success:
                return [[f"Error: {response.error}", "", "", ""]]
            
            chats = response.data.get("chats", [])
            
            # Format chat data for display
            chat_rows = []
            for chat in chats:
                chat_rows.append([
                    chat.get("id", "")[:8] + "...",  # Shortened ID
                    chat.get("title", "Untitled"),
                    chat.get("created_at", "")[:10],  # Date only
                    chat.get("messageCount", 0)
                ])
            
            return chat_rows if chat_rows else [["No chats found", "", "", ""]]
            
        except Exception as e:
            return [[f"Error: {str(e)}", "", "", ""]]
    
    def handle_chart_list_refresh(self, search_term=""):
        """Refresh the chart list."""
        try:
            response = self.client.list_charts(
                page=1,
                limit=50,
                search=search_term
            )
            
            if not response.success:
                return [[f"Error: {response.error}", "", "", ""]]
            
            charts = response.data.get("charts", [])
            
            # Format chart data for display
            chart_rows = []
            for chart in charts:
                chart_rows.append([
                    chart.get("id", "")[:8] + "...",  # Shortened ID
                    chart.get("title", "Untitled"),
                    chart.get("chartType", "Unknown"),
                    chart.get("created_at", "")[:10]  # Date only
                ])
            
            return chart_rows if chart_rows else [["No charts found", "", "", ""]]
            
        except Exception as e:
            return [[f"Error: {str(e)}", "", "", ""]]
    
    def handle_workflow_list_refresh(self, search_term=""):
        """Refresh the workflow list."""
        try:
            response = self.client.list_workflows(
                page=1,
                limit=50,
                search=search_term
            )
            
            if not response.success:
                return [[f"Error: {response.error}", "", "", "", ""]]
            
            workflows = response.data.get("workflows", [])
            
            # Format workflow data for display
            workflow_rows = []
            for workflow in workflows:
                workflow_rows.append([
                    workflow.get("workflowId", "")[:8] + "...",  # Shortened ID
                    workflow.get("name", "Untitled"),
                    workflow.get("description", "")[:50] + ("..." if len(workflow.get("description", "")) > 50 else ""),
                    workflow.get("createdAt", "")[:10]  # Date only
                ])
            
            return workflow_rows if workflow_rows else [["No workflows found", "", "", ""]]
            
        except Exception as e:
            return [[f"Error: {str(e)}", "", "", ""]]
    
    def handle_workflow_execution(self, workflow_id, files, title=""):
        """Execute a workflow with uploaded files."""
        try:
            if not workflow_id:
                return "Please select a workflow first"
            
            if not files:
                return "Please upload files for the workflow"
            
            # Upload files to temporary URLs
            file_urls = []
            for file in files:
                success, file_url = self.data_processor.upload_file_to_temp_url(file.name)
                if success:
                    file_urls.append(file_url)
                else:
                    return f"Error uploading file {file.name}: {file_url}"
            
            # Execute workflow
            response = self.client.execute_workflow(workflow_id, file_urls, title)
            
            if not response.success:
                return f"Error executing workflow: {response.error}"
            
            execution_data = response.data
            
            result_html = f"""
            <div class="feature-card">
                <h4>⚙️ Workflow Execution Started</h4>
                <p><strong>Execution ID:</strong> {execution_data.get('executionId', 'N/A')}</p>
                <p><strong>Status:</strong> {execution_data.get('status', 'Unknown')}</p>
                <p><strong>Started:</strong> {execution_data.get('startedAt', 'N/A')}</p>
                <p><strong>Estimated Duration:</strong> {execution_data.get('estimatedDuration', 'N/A')}</p>
            </div>
            """
            
            return result_html
            
        except Exception as e:
            return f"Error executing workflow: {str(e)}"
    
    def handle_export_data(self, data, format="csv"):
        """Export current data to specified format."""
        try:
            if data is None:
                return None, "No data to export"
            
            success, file_path = self.data_processor.export_dataframe(data, format)
            
            if success:
                return file_path, f"Data exported successfully as {format.upper()}"
            else:
                return None, f"Export failed: {file_path}"
                
        except Exception as e:
            return None, f"Export error: {str(e)}"
    
    def handle_export_chart(self, figure, format="png"):
        """Export current chart to specified format."""
        try:
            if figure is None:
                return None, "No chart to export"
            
            success, file_path = self.chart_generator.export_chart(figure, format)
            
            if success:
                return file_path, f"Chart exported successfully as {format.upper()}"
            else:
                return None, f"Export failed: {file_path}"
                
        except Exception as e:
            return None, f"Export error: {str(e)}"
    
    def _format_analysis_result(self, result_data):
        """Format analysis result for display."""
        if not result_data:
            return "No analysis result received"
        
        # Extract key information from the result
        result_text = result_data.get("result", "")
        steps = result_data.get("steps", [])
        questions = result_data.get("questions", "")
        
        # Format steps
        steps_html = ""
        if steps:
            steps_html = "<h5>Analysis Steps:</h5><ul>"
            for step in steps:
                status_icon = "✅" if step.get("status") == 2 else "⏳"
                steps_html += f"<li>{status_icon} {step.get('content', '')}</li>"
            steps_html += "</ul>"
        
        # Format suggested questions
        questions_html = ""
        if questions:
            try:
                question_list = json.loads(questions) if isinstance(questions, str) else questions
                if question_list:
                    questions_html = "<h5>Suggested Follow-up Questions:</h5><ul>"
                    for q in question_list[:3]:  # Show first 3 questions
                        questions_html += f"<li>{q}</li>"
                    questions_html += "</ul>"
            except:
                pass
        
        return f"""
        <div class="feature-card">
            <h4>🤖 AI Analysis Result</h4>
            <div style="margin: 1rem 0;">
                {result_text}
            </div>
            {steps_html}
            {questions_html}
        </div>
        """
