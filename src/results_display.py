"""
Results display components for the AiBiao AI Data Analysis System.
"""

import gradio as gr
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from typing import Dict, List, Optional, Any, Tuple
import json
import base64
from io import BytesIO

class ResultsDisplay:
    """Handles the display of analysis results and visualizations."""
    
    def __init__(self):
        self.max_preview_rows = 100
        self.chart_height = 500
    
    def create_data_summary_card(self, df: pd.DataFrame) -> str:
        """Create a summary card for the dataset."""
        if df is None or df.empty:
            return "<div class='feature-card'>No data available</div>"
        
        # Basic statistics
        n_rows, n_cols = df.shape
        numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
        categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
        datetime_cols = df.select_dtypes(include=['datetime']).columns.tolist()
        
        # Missing values
        missing_data = df.isnull().sum()
        total_missing = missing_data.sum()
        missing_percentage = (total_missing / (n_rows * n_cols)) * 100
        
        # Memory usage
        memory_usage = df.memory_usage(deep=True).sum() / 1024 / 1024  # MB
        
        summary_html = f"""
        <div class="feature-card">
            <h4>📊 Dataset Summary</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
                <div>
                    <h5>Basic Info</h5>
                    <p><strong>Rows:</strong> {n_rows:,}</p>
                    <p><strong>Columns:</strong> {n_cols}</p>
                    <p><strong>Memory:</strong> {memory_usage:.2f} MB</p>
                </div>
                <div>
                    <h5>Column Types</h5>
                    <p><strong>Numeric:</strong> {len(numeric_cols)}</p>
                    <p><strong>Categorical:</strong> {len(categorical_cols)}</p>
                    <p><strong>DateTime:</strong> {len(datetime_cols)}</p>
                </div>
                <div>
                    <h5>Data Quality</h5>
                    <p><strong>Missing Values:</strong> {total_missing:,}</p>
                    <p><strong>Missing %:</strong> {missing_percentage:.2f}%</p>
                    <p><strong>Complete Rows:</strong> {df.dropna().shape[0]:,}</p>
                </div>
            </div>
        </div>
        """
        
        return summary_html
    
    def create_column_analysis_card(self, df: pd.DataFrame) -> str:
        """Create a detailed column analysis card."""
        if df is None or df.empty:
            return "<div class='feature-card'>No data available</div>"
        
        analysis_html = """
        <div class="feature-card">
            <h4>📋 Column Analysis</h4>
            <div style="overflow-x: auto; margin-top: 1rem;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background-color: #f8f9fa;">
                            <th style="padding: 8px; border: 1px solid #dee2e6;">Column</th>
                            <th style="padding: 8px; border: 1px solid #dee2e6;">Type</th>
                            <th style="padding: 8px; border: 1px solid #dee2e6;">Non-Null</th>
                            <th style="padding: 8px; border: 1px solid #dee2e6;">Unique</th>
                            <th style="padding: 8px; border: 1px solid #dee2e6;">Sample Values</th>
                        </tr>
                    </thead>
                    <tbody>
        """
        
        for col in df.columns:
            dtype = str(df[col].dtype)
            non_null = df[col].count()
            unique_count = df[col].nunique()
            
            # Get sample values (non-null)
            sample_values = df[col].dropna().head(3).tolist()
            sample_str = ", ".join([str(v)[:20] + ("..." if len(str(v)) > 20 else "") for v in sample_values])
            
            analysis_html += f"""
                        <tr>
                            <td style="padding: 8px; border: 1px solid #dee2e6;"><strong>{col}</strong></td>
                            <td style="padding: 8px; border: 1px solid #dee2e6;">{dtype}</td>
                            <td style="padding: 8px; border: 1px solid #dee2e6;">{non_null:,}</td>
                            <td style="padding: 8px; border: 1px solid #dee2e6;">{unique_count:,}</td>
                            <td style="padding: 8px; border: 1px solid #dee2e6;">{sample_str}</td>
                        </tr>
            """
        
        analysis_html += """
                    </tbody>
                </table>
            </div>
        </div>
        """
        
        return analysis_html
    
    def create_statistics_card(self, df: pd.DataFrame) -> str:
        """Create a statistics summary card for numeric columns."""
        if df is None or df.empty:
            return "<div class='feature-card'>No data available</div>"
        
        numeric_df = df.select_dtypes(include=['number'])
        
        if numeric_df.empty:
            return "<div class='feature-card'><h4>📈 Statistics</h4><p>No numeric columns found</p></div>"
        
        stats = numeric_df.describe()
        
        stats_html = """
        <div class="feature-card">
            <h4>📈 Numeric Statistics</h4>
            <div style="overflow-x: auto; margin-top: 1rem;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background-color: #f8f9fa;">
                            <th style="padding: 8px; border: 1px solid #dee2e6;">Statistic</th>
        """
        
        # Add column headers
        for col in stats.columns:
            stats_html += f'<th style="padding: 8px; border: 1px solid #dee2e6;">{col}</th>'
        
        stats_html += """
                        </tr>
                    </thead>
                    <tbody>
        """
        
        # Add statistics rows
        for stat in stats.index:
            stats_html += f"""
                        <tr>
                            <td style="padding: 8px; border: 1px solid #dee2e6;"><strong>{stat}</strong></td>
            """
            for col in stats.columns:
                value = stats.loc[stat, col]
                formatted_value = f"{value:.2f}" if isinstance(value, (int, float)) else str(value)
                stats_html += f'<td style="padding: 8px; border: 1px solid #dee2e6;">{formatted_value}</td>'
            stats_html += "</tr>"
        
        stats_html += """
                    </tbody>
                </table>
            </div>
        </div>
        """
        
        return stats_html
    
    def create_missing_data_visualization(self, df: pd.DataFrame) -> go.Figure:
        """Create a visualization for missing data patterns."""
        if df is None or df.empty:
            return self._create_empty_figure("No data available")
        
        # Calculate missing data percentage for each column
        missing_data = df.isnull().sum()
        missing_percentage = (missing_data / len(df)) * 100
        
        # Filter columns with missing data
        missing_cols = missing_percentage[missing_percentage > 0].sort_values(ascending=True)
        
        if missing_cols.empty:
            return self._create_empty_figure("No missing data found")
        
        # Create horizontal bar chart
        fig = go.Figure(data=[
            go.Bar(
                y=missing_cols.index,
                x=missing_cols.values,
                orientation='h',
                marker_color='rgba(255, 99, 132, 0.8)',
                text=[f"{val:.1f}%" for val in missing_cols.values],
                textposition='auto'
            )
        ])
        
        fig.update_layout(
            title="Missing Data by Column",
            xaxis_title="Missing Data Percentage",
            yaxis_title="Columns",
            template="plotly_white",
            height=max(300, len(missing_cols) * 30),
            margin=dict(l=100, r=40, t=60, b=40)
        )
        
        return fig
    
    def create_correlation_heatmap(self, df: pd.DataFrame) -> go.Figure:
        """Create a correlation heatmap for numeric columns."""
        if df is None or df.empty:
            return self._create_empty_figure("No data available")
        
        numeric_df = df.select_dtypes(include=['number'])
        
        if numeric_df.shape[1] < 2:
            return self._create_empty_figure("Need at least 2 numeric columns for correlation")
        
        # Calculate correlation matrix
        corr_matrix = numeric_df.corr()
        
        # Create heatmap
        fig = go.Figure(data=go.Heatmap(
            z=corr_matrix.values,
            x=corr_matrix.columns,
            y=corr_matrix.columns,
            colorscale='RdBu',
            zmid=0,
            text=corr_matrix.round(2).values,
            texttemplate="%{text}",
            textfont={"size": 10},
            hoverongaps=False
        ))
        
        fig.update_layout(
            title="Correlation Matrix",
            template="plotly_white",
            height=500,
            width=500
        )
        
        return fig
    
    def create_distribution_plots(self, df: pd.DataFrame) -> List[go.Figure]:
        """Create distribution plots for numeric columns."""
        if df is None or df.empty:
            return [self._create_empty_figure("No data available")]
        
        numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
        
        if not numeric_cols:
            return [self._create_empty_figure("No numeric columns found")]
        
        figures = []
        
        # Create histogram for each numeric column (max 6)
        for col in numeric_cols[:6]:
            fig = px.histogram(
                df,
                x=col,
                title=f"Distribution of {col}",
                template="plotly_white",
                marginal="box"  # Add box plot on top
            )
            
            fig.update_layout(
                height=400,
                showlegend=False
            )
            
            figures.append(fig)
        
        return figures
    
    def create_export_options(self) -> str:
        """Create export options HTML."""
        export_html = """
        <div class="feature-card">
            <h4>📥 Export Options</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-top: 1rem;">
                <div>
                    <h5>Data Formats</h5>
                    <button class="btn btn-sm btn-outline-primary" onclick="exportData('csv')">CSV</button>
                    <button class="btn btn-sm btn-outline-primary" onclick="exportData('xlsx')">Excel</button>
                    <button class="btn btn-sm btn-outline-primary" onclick="exportData('json')">JSON</button>
                </div>
                <div>
                    <h5>Chart Formats</h5>
                    <button class="btn btn-sm btn-outline-secondary" onclick="exportChart('png')">PNG</button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="exportChart('svg')">SVG</button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="exportChart('html')">HTML</button>
                </div>
            </div>
        </div>
        """
        
        return export_html
    
    def _create_empty_figure(self, message: str) -> go.Figure:
        """Create an empty figure with a message."""
        fig = go.Figure()
        fig.add_annotation(
            text=message,
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        fig.update_layout(
            template="plotly_white",
            height=400,
            xaxis=dict(showgrid=False, showticklabels=False, zeroline=False),
            yaxis=dict(showgrid=False, showticklabels=False, zeroline=False)
        )
        return fig
    
    def format_analysis_results(self, analysis_data: Dict[str, Any]) -> str:
        """Format AI analysis results for display."""
        if not analysis_data:
            return "<div class='feature-card'>No analysis results available</div>"
        
        result_text = analysis_data.get("result", "")
        steps = analysis_data.get("steps", [])
        questions = analysis_data.get("questions", "")
        
        # Format the main result
        result_html = f"""
        <div class="feature-card">
            <h4>🤖 AI Analysis Results</h4>
            <div style="margin: 1rem 0; padding: 1rem; background-color: #f8f9fa; border-radius: 8px;">
                {result_text}
            </div>
        """
        
        # Add processing steps if available
        if steps:
            result_html += "<h5>Processing Steps:</h5><ul>"
            for step in steps:
                status_icon = "✅" if step.get("status") == 2 else "⏳" if step.get("status") == 1 else "❌"
                result_html += f"<li>{status_icon} {step.get('content', '')}</li>"
            result_html += "</ul>"
        
        # Add suggested questions if available
        if questions:
            try:
                if isinstance(questions, str):
                    question_list = json.loads(questions)
                else:
                    question_list = questions
                
                if question_list:
                    result_html += "<h5>Suggested Follow-up Questions:</h5><ul>"
                    for question in question_list[:5]:  # Show first 5 questions
                        result_html += f"<li>{question}</li>"
                    result_html += "</ul>"
            except:
                pass
        
        result_html += "</div>"
        
        return result_html
