"""
AiBiao API Client for handling all API interactions.
"""

import requests
import json
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

class APIStatus(Enum):
    """API response status enumeration."""
    SUCCESS = "success"
    ERROR = "error"
    WAITING = "waiting"
    RUNNING = "running"

@dataclass
class APIResponse:
    """Standardized API response structure."""
    success: bool
    data: Optional[Dict[str, Any]] = None
    message: str = ""
    error: Optional[str] = None

class AiBiaoClient:
    """Client for interacting with the AiBiao API."""
    
    def __init__(self, api_key: str = "", base_url: str = "https://aibiao.cn/api/enterprise/external"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = requests.Session()
        self._setup_session()
    
    def _setup_session(self):
        """Setup the requests session with default headers."""
        self.session.headers.update({
            "Content-Type": "application/json",
            "User-Agent": "AiBiao-Analysis-System/1.0"
        })
    
    def set_api_key(self, api_key: str):
        """Set the API key for authentication."""
        self.api_key = api_key
        if api_key:
            self.session.headers["Authorization"] = f"Bearer {api_key}"
        elif "Authorization" in self.session.headers:
            del self.session.headers["Authorization"]
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> APIResponse:
        """Make a request to the AiBiao API."""
        if not self.api_key:
            return APIResponse(
                success=False,
                error="API key is required. Please configure your API key first."
            )
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            if method.upper() == "GET":
                response = self.session.get(url, params=data)
            else:
                response = self.session.post(url, json=data)
            
            response.raise_for_status()
            
            # Handle different response formats
            if response.headers.get('content-type', '').startswith('application/json'):
                result = response.json()
            else:
                result = {"message": response.text}
            
            return APIResponse(
                success=result.get("success", True),
                data=result.get("data", result),
                message=result.get("message", "")
            )
            
        except requests.exceptions.RequestException as e:
            return APIResponse(
                success=False,
                error=f"Request failed: {str(e)}"
            )
        except json.JSONDecodeError as e:
            return APIResponse(
                success=False,
                error=f"Invalid JSON response: {str(e)}"
            )
        except Exception as e:
            return APIResponse(
                success=False,
                error=f"Unexpected error: {str(e)}"
            )
    
    def check_api_key_status(self) -> APIResponse:
        """Check the status of the current API key."""
        return self._make_request("GET", "/api-key-status")
    
    def create_new_chat(self, file_urls: List[str], title: Optional[str] = None) -> APIResponse:
        """Create a new chat session with file URLs."""
        data = {
            "fileUrls": file_urls,
            "title": title
        }
        return self._make_request("POST", "/new-chat", data)
    
    def append_chat_message(self, chat_id: str, message: str) -> APIResponse:
        """Append a message to an existing chat session."""
        data = {
            "chatId": chat_id,
            "message": message
        }
        return self._make_request("POST", "/append-chat", data)
    
    def list_chats(self, page: int = 1, limit: int = 20, include_messages: bool = False, 
                   search: str = "") -> APIResponse:
        """List all chat sessions with pagination and filtering."""
        data = {
            "page": page,
            "limit": limit,
            "includeMessages": include_messages,
            "search": search
        }
        return self._make_request("POST", "/list-chats", data)
    
    def get_chat(self, chat_id: str, include_messages: bool = True, 
                 message_limit: int = 100) -> APIResponse:
        """Get a specific chat session with its messages."""
        data = {
            "chatId": chat_id,
            "includeMessages": include_messages,
            "messageLimit": message_limit
        }
        return self._make_request("POST", "/get-chat", data)
    
    def update_chat_title(self, chat_id: str, title: str) -> APIResponse:
        """Update the title of a chat session."""
        data = {
            "chatId": chat_id,
            "title": title
        }
        return self._make_request("POST", "/update-chat-title", data)
    
    def delete_chat(self, chat_id: str) -> APIResponse:
        """Delete a chat session."""
        data = {"chatId": chat_id}
        return self._make_request("POST", "/delete-chat", data)
    
    def create_new_chart(self, file_url: str, prompt: str) -> APIResponse:
        """Create a new chart from a file URL with a prompt."""
        data = {
            "fileUrl": file_url,
            "prompt": prompt
        }
        return self._make_request("POST", "/new-chart", data)
    
    def list_charts(self, page: int = 1, limit: int = 20, search: str = "", 
                    sort_by: str = "created_at", sort_order: str = "desc") -> APIResponse:
        """List all charts with pagination and sorting."""
        data = {
            "page": page,
            "limit": limit,
            "search": search,
            "sortBy": sort_by,
            "sortOrder": sort_order
        }
        return self._make_request("POST", "/list-charts", data)
    
    def delete_chart(self, chart_id: str) -> APIResponse:
        """Delete a chart."""
        data = {"chartId": chart_id}
        return self._make_request("POST", "/delete-chart", data)
    
    def save_workflow(self, chat_id: str, workflow_name: str, 
                      description: str = "") -> APIResponse:
        """Save a workflow based on a chat session."""
        data = {
            "chatId": chat_id,
            "workflowName": workflow_name,
            "description": description
        }
        return self._make_request("POST", "/save-workflow", data)
    
    def list_workflows(self, page: int = 1, limit: int = 20, search: str = "",
                       sort_by: str = "updatedAt", sort_order: str = "desc") -> APIResponse:
        """List all workflows with pagination and filtering."""
        data = {
            "page": page,
            "limit": limit,
            "search": search,
            "sortBy": sort_by,
            "sortOrder": sort_order
        }
        return self._make_request("POST", "/list-workflows", data)
    
    def execute_workflow(self, workflow_id: str, file_urls: List[str], 
                         title: Optional[str] = None) -> APIResponse:
        """Execute a workflow with input files."""
        data = {
            "workflowId": workflow_id,
            "fileUrls": file_urls,
            "title": title
        }
        return self._make_request("POST", "/execute-workflow", data)
    
    def update_workflow_name(self, workflow_id: str, name: str, 
                             description: str = "") -> APIResponse:
        """Update workflow name and description."""
        data = {
            "workflowId": workflow_id,
            "name": name,
            "description": description
        }
        return self._make_request("POST", "/update-workflow-name", data)
    
    def delete_workflow(self, workflow_id: str) -> APIResponse:
        """Delete a workflow."""
        data = {"workflowId": workflow_id}
        return self._make_request("POST", "/delete-workflow", data)
