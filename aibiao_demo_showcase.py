#!/usr/bin/env python3
"""
AiBiao API集成展示版本
模拟真实的AiBiao API响应，展示现代化的界面设计和数据分析结果展示
"""

import gradio as gr
import pandas as pd
import numpy as np
import json
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple

# 模拟AiBiao API响应数据
class MockAiBiaoAPI:
    """模拟AiBiao API的响应"""
    
    def __init__(self):
        self.chat_sessions = []
        self.charts = []
        self.workflows = []
    
    def simulate_chat_analysis(self, prompt: str, file_url: str = "") -> Dict[str, Any]:
        """模拟聊天分析API响应"""
        
        # 模拟不同类型的分析结果
        analysis_results = [
            {
                "result": "根据您提供的销售数据分析，我发现以下关键洞察：\n\n📈 **销售趋势分析**\n- Q3季度销售额同比增长32%，表现优异\n- 产品A销量占总销量的45%，是主力产品\n- 华东地区贡献了60%的销售额\n\n🎯 **关键发现**\n- 移动端订单占比达到78%，移动化趋势明显\n- 客单价较去年同期提升15%\n- 复购率达到42%，客户忠诚度较高\n\n💡 **建议**\n1. 加大产品A的库存和推广力度\n2. 重点关注华东地区的市场拓展\n3. 优化移动端用户体验",
                "title": "销售数据深度分析报告",
                "steps": [
                    {"status": 2, "content": "数据预处理和清洗"},
                    {"status": 2, "content": "销售趋势分析"},
                    {"status": 2, "content": "产品表现评估"},
                    {"status": 2, "content": "地域分布分析"},
                    {"status": 2, "content": "生成分析报告"}
                ],
                "questions": ["产品A的具体销售数据是什么？", "华东地区的增长原因是什么？", "如何进一步提升复购率？", "移动端转化率如何优化？"]
            },
            {
                "result": "您的用户行为数据分析已完成：\n\n👥 **用户画像分析**\n- 主要用户群体：25-35岁，占比52%\n- 活跃时段：晚上8-10点，周末全天\n- 地域分布：一二线城市用户占比73%\n\n📱 **行为特征**\n- 平均会话时长：8.5分钟\n- 页面跳出率：35%（行业平均45%）\n- 功能使用率：搜索85%，筛选62%，收藏38%\n\n🔍 **关键洞察**\n- 用户对个性化推荐的点击率达到23%\n- 社交分享功能使用率偏低，仅12%\n- 客服咨询主要集中在产品使用问题",
                "title": "用户行为深度洞察",
                "steps": [
                    {"status": 2, "content": "用户数据采集"},
                    {"status": 2, "content": "行为路径分析"},
                    {"status": 2, "content": "用户画像构建"},
                    {"status": 2, "content": "行为模式识别"},
                    {"status": 2, "content": "洞察报告生成"}
                ],
                "questions": ["如何提升社交分享功能的使用率？", "个性化推荐算法如何优化？", "用户流失的主要原因是什么？"]
            }
        ]
        
        # 随机选择一个分析结果
        result = random.choice(analysis_results)
        
        # 添加聊天ID
        chat_id = f"chat_{int(time.time())}_{random.randint(1000, 9999)}"
        result["chatId"] = chat_id
        
        return result
    
    def simulate_chart_creation(self, prompt: str, file_url: str = "") -> Dict[str, Any]:
        """模拟图表创建API响应"""
        
        chart_types = [
            {
                "chartId": f"chart_{int(time.time())}_{random.randint(1000, 9999)}",
                "title": "销售趋势分析图表",
                "chartUrl": "https://aibiao.cn/chart/edit/abc123def456",
                "shareUrl": "https://aibiao.cn/chart/share/abc123def456",
                "chartType": "line",
                "description": "展示了过去12个月的销售趋势变化"
            },
            {
                "chartId": f"chart_{int(time.time())}_{random.randint(1000, 9999)}",
                "title": "产品销量分布图",
                "chartUrl": "https://aibiao.cn/chart/edit/def456ghi789",
                "shareUrl": "https://aibiao.cn/chart/share/def456ghi789",
                "chartType": "pie",
                "description": "各产品类别的销量占比分析"
            },
            {
                "chartId": f"chart_{int(time.time())}_{random.randint(1000, 9999)}",
                "title": "地域销售热力图",
                "chartUrl": "https://aibiao.cn/chart/edit/ghi789jkl012",
                "shareUrl": "https://aibiao.cn/chart/share/ghi789jkl012",
                "chartType": "heatmap",
                "description": "全国各地区销售额分布热力图"
            }
        ]
        
        return random.choice(chart_types)

# 创建现代化的结果展示组件
class ModernDisplayManager:
    """现代化的结果展示管理器"""
    
    def format_analysis_result(self, result_data: Dict[str, Any]) -> str:
        """格式化分析结果为现代化界面"""
        
        result_text = result_data.get("result", "")
        title = result_data.get("title", "AI数据分析")
        steps = result_data.get("steps", [])
        questions = result_data.get("questions", [])
        chat_id = result_data.get("chatId", "")
        
        # 创建现代化的结果展示
        html = f"""
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 16px; padding: 2.5rem; margin: 1.5rem 0; color: white; box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);">
            <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                <div style="background: rgba(255,255,255,0.2); border-radius: 12px; padding: 0.75rem; margin-right: 1rem;">
                    <span style="font-size: 1.5rem;">🤖</span>
                </div>
                <div>
                    <h2 style="margin: 0; font-size: 1.8rem; font-weight: 700;">AI分析完成</h2>
                    <p style="margin: 0.25rem 0 0 0; opacity: 0.9; font-size: 1.1rem;">{title}</p>
                </div>
            </div>
        </div>
        
        <div style="background: white; border-radius: 16px; padding: 2.5rem; margin: 1.5rem 0; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9;">
            <div style="display: flex; align-items: center; margin-bottom: 1.5rem;">
                <div style="background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 8px; padding: 0.5rem; margin-right: 1rem;">
                    <span style="color: white; font-size: 1.2rem;">📊</span>
                </div>
                <h3 style="color: #1e293b; margin: 0; font-size: 1.4rem; font-weight: 600;">分析结果</h3>
            </div>
            <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-radius: 12px; padding: 2rem; border-left: 4px solid #667eea; line-height: 1.8;">
                <div style="color: #334155; font-size: 1rem; white-space: pre-line;">{result_text}</div>
            </div>
        </div>
        """
        
        # 添加处理步骤
        if steps:
            html += """
            <div style="background: white; border-radius: 16px; padding: 2.5rem; margin: 1.5rem 0; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9;">
                <div style="display: flex; align-items: center; margin-bottom: 1.5rem;">
                    <div style="background: linear-gradient(135deg, #10b981, #059669); border-radius: 8px; padding: 0.5rem; margin-right: 1rem;">
                        <span style="color: white; font-size: 1.2rem;">⚙️</span>
                    </div>
                    <h3 style="color: #1e293b; margin: 0; font-size: 1.4rem; font-weight: 600;">处理步骤</h3>
                </div>
                <div style="display: grid; gap: 1rem;">
            """
            
            for i, step in enumerate(steps):
                status = step.get("status", 0)
                content = step.get("content", "")
                
                if status == 2:  # 完成
                    icon = "✅"
                    bg_color = "#dcfce7"
                    border_color = "#16a34a"
                    text_color = "#15803d"
                elif status == 1:  # 进行中
                    icon = "⏳"
                    bg_color = "#fef3c7"
                    border_color = "#d97706"
                    text_color = "#92400e"
                else:  # 未开始或失败
                    icon = "⭕"
                    bg_color = "#fee2e2"
                    border_color = "#dc2626"
                    text_color = "#991b1b"
                
                html += f"""
                    <div style="display: flex; align-items: center; padding: 1rem 1.5rem; background: {bg_color}; border-radius: 12px; border-left: 4px solid {border_color}; transition: all 0.3s ease;">
                        <span style="margin-right: 1rem; font-size: 1.3rem;">{icon}</span>
                        <span style="color: {text_color}; font-weight: 500; font-size: 1rem;">{content}</span>
                    </div>
                """
            
            html += "</div></div>"
        
        # 添加建议问题
        if questions:
            html += """
            <div style="background: white; border-radius: 16px; padding: 2.5rem; margin: 1.5rem 0; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9;">
                <div style="display: flex; align-items: center; margin-bottom: 1.5rem;">
                    <div style="background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 8px; padding: 0.5rem; margin-right: 1rem;">
                        <span style="color: white; font-size: 1.2rem;">💡</span>
                    </div>
                    <h3 style="color: #1e293b; margin: 0; font-size: 1.4rem; font-weight: 600;">建议的后续问题</h3>
                </div>
                <div style="display: grid; gap: 1rem;">
            """
            
            for i, question in enumerate(questions[:4]):  # 最多显示4个问题
                html += f"""
                    <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-radius: 12px; padding: 1.25rem 1.5rem; border: 2px solid #e2e8f0; cursor: pointer; transition: all 0.3s ease; position: relative; overflow: hidden;" 
                         onmouseover="this.style.borderColor='#667eea'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(102, 126, 234, 0.15)';" 
                         onmouseout="this.style.borderColor='#e2e8f0'; this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                        <div style="display: flex; align-items: center;">
                            <span style="color: #667eea; font-size: 1.1rem; margin-right: 0.75rem;">❓</span>
                            <span style="color: #475569; font-size: 1rem; font-weight: 500;">{question}</span>
                        </div>
                    </div>
                """
            
            html += "</div></div>"
        
        # 添加会话信息
        if chat_id:
            html += f"""
            <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-radius: 12px; padding: 1.5rem; margin: 1.5rem 0; border: 1px solid #e2e8f0;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div style="display: flex; align-items: center;">
                        <span style="color: #667eea; font-size: 1.1rem; margin-right: 0.75rem;">💬</span>
                        <span style="color: #64748b; font-size: 0.95rem;">会话ID: </span>
                        <code style="background: #e2e8f0; color: #475569; padding: 0.25rem 0.75rem; border-radius: 6px; font-family: 'SF Mono', 'Monaco', 'Inconsolata', monospace; font-size: 0.9rem; margin-left: 0.5rem;">{chat_id[:16]}...</code>
                    </div>
                    <div style="color: #10b981; font-size: 0.9rem; font-weight: 500;">✓ 分析完成</div>
                </div>
            </div>
            """
        
        return html
    
    def format_chart_result(self, chart_data: Dict[str, Any]) -> Tuple[str, str]:
        """格式化图表结果"""
        
        chart_id = chart_data.get("chartId", "")
        title = chart_data.get("title", "AI生成图表")
        chart_url = chart_data.get("chartUrl", "")
        share_url = chart_data.get("shareUrl", "")
        chart_type = chart_data.get("chartType", "")
        description = chart_data.get("description", "")
        
        # 状态信息
        status_html = f"""
        <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 16px; padding: 2.5rem; margin: 1.5rem 0; color: white; box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);">
            <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                <div style="background: rgba(255,255,255,0.2); border-radius: 12px; padding: 0.75rem; margin-right: 1rem;">
                    <span style="font-size: 1.5rem;">📈</span>
                </div>
                <div>
                    <h2 style="margin: 0; font-size: 1.8rem; font-weight: 700;">图表生成成功</h2>
                    <p style="margin: 0.25rem 0 0 0; opacity: 0.9; font-size: 1.1rem;">{title}</p>
                </div>
            </div>
        </div>
        
        <div style="background: white; border-radius: 16px; padding: 2.5rem; margin: 1.5rem 0; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9;">
            <div style="display: flex; align-items: center; margin-bottom: 1.5rem;">
                <div style="background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 8px; padding: 0.5rem; margin-right: 1rem;">
                    <span style="color: white; font-size: 1.2rem;">🔗</span>
                </div>
                <h3 style="color: #1e293b; margin: 0; font-size: 1.4rem; font-weight: 600;">图表链接</h3>
            </div>
            <div style="display: grid; gap: 1.5rem;">
                <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-radius: 12px; padding: 1.5rem; border: 1px solid #e2e8f0;">
                    <div style="display: flex; align-items: center; margin-bottom: 0.75rem;">
                        <span style="color: #667eea; font-size: 1.1rem; margin-right: 0.5rem;">📊</span>
                        <span style="color: #475569; font-weight: 600;">编辑链接（需要登录）</span>
                    </div>
                    <a href="{chart_url}" target="_blank" style="color: #667eea; text-decoration: none; font-family: 'SF Mono', monospace; font-size: 0.9rem; word-break: break-all; background: #f1f5f9; padding: 0.5rem; border-radius: 6px; display: block;">{chart_url}</a>
                </div>
                <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-radius: 12px; padding: 1.5rem; border: 1px solid #e2e8f0;">
                    <div style="display: flex; align-items: center; margin-bottom: 0.75rem;">
                        <span style="color: #10b981; font-size: 1.1rem; margin-right: 0.5rem;">🔗</span>
                        <span style="color: #475569; font-weight: 600;">分享链接（免登录查看）</span>
                    </div>
                    <a href="{share_url}" target="_blank" style="color: #10b981; text-decoration: none; font-family: 'SF Mono', monospace; font-size: 0.9rem; word-break: break-all; background: #f1f5f9; padding: 0.5rem; border-radius: 6px; display: block;">{share_url}</a>
                </div>
            </div>
            <div style="margin-top: 1.5rem; padding: 1.25rem; background: #fef7ff; border-radius: 12px; border-left: 4px solid #a855f7;">
                <p style="margin: 0; color: #7c3aed; font-size: 0.95rem;"><strong>描述:</strong> {description}</p>
            </div>
        </div>
        
        <div style="background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-radius: 12px; padding: 1.5rem; margin: 1.5rem 0; border: 1px solid #e2e8f0;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center;">
                    <span style="color: #667eea; font-size: 1.1rem; margin-right: 0.75rem;">📋</span>
                    <span style="color: #64748b; font-size: 0.95rem;">图表ID: </span>
                    <code style="background: #e2e8f0; color: #475569; padding: 0.25rem 0.75rem; border-radius: 6px; font-family: 'SF Mono', monospace; font-size: 0.9rem; margin-left: 0.5rem;">{chart_id[:16]}...</code>
                </div>
                <div style="color: #10b981; font-size: 0.9rem; font-weight: 500;">✓ 生成完成</div>
            </div>
        </div>
        """
        
        # 图表预览（模拟iframe）
        chart_preview = f"""
        <div style="background: white; border-radius: 16px; padding: 2rem; margin: 1.5rem 0; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); border: 1px solid #f1f5f9;">
            <div style="display: flex; align-items: center; margin-bottom: 1.5rem;">
                <div style="background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 8px; padding: 0.5rem; margin-right: 1rem;">
                    <span style="color: white; font-size: 1.2rem;">📊</span>
                </div>
                <h3 style="color: #1e293b; margin: 0; font-size: 1.4rem; font-weight: 600;">图表预览</h3>
            </div>
            <div style="border: 2px solid #e2e8f0; border-radius: 12px; overflow: hidden; background: #f8fafc;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1rem; text-align: center;">
                    <p style="margin: 0; font-size: 1.1rem; font-weight: 500;">🎯 AiBiao图表预览</p>
                    <p style="margin: 0.5rem 0 0 0; opacity: 0.9; font-size: 0.9rem;">点击分享链接查看完整交互式图表</p>
                </div>
                <div style="padding: 3rem; text-align: center; background: white;">
                    <div style="background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%); border-radius: 12px; padding: 2rem; border: 2px dashed #cbd5e1;">
                        <span style="font-size: 3rem; color: #94a3b8;">📈</span>
                        <p style="margin: 1rem 0 0 0; color: #64748b; font-size: 1.1rem; font-weight: 500;">{title}</p>
                        <p style="margin: 0.5rem 0 0 0; color: #94a3b8; font-size: 0.9rem;">类型: {chart_type.upper()}</p>
                    </div>
                </div>
            </div>
        </div>
        """
        
        return status_html, chart_preview

# 创建演示应用
def create_demo_app():
    """创建AiBiao API集成演示应用"""
    
    mock_api = MockAiBiaoAPI()
    display_manager = ModernDisplayManager()
    
    # 自定义CSS样式
    custom_css = """
    .gradio-container {
        max-width: 1200px !important;
        margin: 0 auto !important;
    }
    
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 16px;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    }
    
    .feature-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: 1px solid #f1f5f9;
    }
    
    .demo-badge {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        display: inline-block;
        margin-bottom: 1rem;
    }
    """
    
    with gr.Blocks(css=custom_css, title="AiBiao AI数据分析系统 - 演示版", theme=gr.themes.Soft()) as app:
        
        # 头部
        gr.HTML("""
        <div class="main-header">
            <div class="demo-badge">🚀 演示版本</div>
            <h1 style="margin: 0; font-size: 2.5rem; font-weight: 700;">AiBiao AI数据分析系统</h1>
            <p style="margin: 1rem 0 0 0; font-size: 1.2rem; opacity: 0.9;">体验现代化的AI驱动数据分析平台</p>
        </div>
        """)
        
        with gr.Tab("🤖 AI数据分析", id="analysis"):
            with gr.Row():
                with gr.Column(scale=1):
                    gr.HTML("<h3>📊 数据输入</h3>")
                    
                    # 数据输入方式
                    input_method = gr.Radio(
                        choices=["文件上传", "URL导入", "直接输入"],
                        value="直接输入",
                        label="数据输入方式"
                    )
                    
                    # 文件上传
                    file_upload = gr.File(
                        label="上传数据文件",
                        file_types=[".csv", ".xlsx", ".json"],
                        visible=False
                    )
                    
                    # URL输入
                    url_input = gr.Textbox(
                        label="数据文件URL",
                        placeholder="https://example.com/data.csv",
                        visible=False
                    )
                    
                    # 直接输入
                    direct_input = gr.Textbox(
                        label="CSV数据",
                        placeholder="产品,销量,价格\n产品A,1000,99\n产品B,800,129\n产品C,1200,79",
                        lines=8,
                        value="产品,销量,价格,地区\n产品A,1000,99,华东\n产品B,800,129,华南\n产品C,1200,79,华北\n产品D,600,159,华西\n产品E,900,89,华中",
                        visible=True
                    )
                    
                    # 分析提示
                    analysis_prompt = gr.Textbox(
                        label="分析需求",
                        placeholder="请描述您想要进行的数据分析...",
                        lines=3,
                        value="请分析这个销售数据，找出销售趋势和关键洞察"
                    )
                    
                    # 分析按钮
                    analyze_btn = gr.Button("🚀 开始AI分析", variant="primary", size="lg")
                
                with gr.Column(scale=2):
                    gr.HTML("<h3>📈 分析结果</h3>")
                    
                    # 分析结果显示
                    analysis_result = gr.HTML(
                        value="""
                        <div class="feature-card">
                            <div style="text-align: center; padding: 2rem; color: #64748b;">
                                <span style="font-size: 3rem;">🤖</span>
                                <p style="margin: 1rem 0 0 0; font-size: 1.1rem;">点击"开始AI分析"按钮体验AiBiao AI的强大分析能力</p>
                            </div>
                        </div>
                        """
                    )
        
        with gr.Tab("📈 图表生成", id="charts"):
            with gr.Row():
                with gr.Column(scale=1):
                    gr.HTML("<h3>🎨 图表配置</h3>")
                    
                    chart_data_input = gr.Textbox(
                        label="数据输入",
                        placeholder="输入您的数据或数据URL...",
                        lines=6,
                        value="月份,销售额,利润\n1月,120000,25000\n2月,135000,28000\n3月,148000,32000\n4月,162000,35000\n5月,178000,38000\n6月,195000,42000"
                    )
                    
                    chart_prompt = gr.Textbox(
                        label="图表需求",
                        placeholder="描述您想要的图表类型和样式...",
                        lines=3,
                        value="请生成一个展示销售额和利润趋势的双轴折线图"
                    )
                    
                    create_chart_btn = gr.Button("🎨 生成图表", variant="primary", size="lg")
                
                with gr.Column(scale=2):
                    gr.HTML("<h3>📊 图表结果</h3>")
                    
                    chart_result = gr.HTML(
                        value="""
                        <div class="feature-card">
                            <div style="text-align: center; padding: 2rem; color: #64748b;">
                                <span style="font-size: 3rem;">📈</span>
                                <p style="margin: 1rem 0 0 0; font-size: 1.1rem;">点击"生成图表"按钮体验AiBiao的智能图表生成功能</p>
                            </div>
                        </div>
                        """
                    )
        
        # 事件处理函数
        def handle_input_method_change(method):
            """处理输入方式变化"""
            return (
                gr.update(visible=(method == "文件上传")),
                gr.update(visible=(method == "URL导入")),
                gr.update(visible=(method == "直接输入"))
            )
        
        def handle_analysis(input_method, file_upload, url_input, direct_input, prompt):
            """处理数据分析请求"""
            
            # 模拟API调用延迟
            time.sleep(1)
            
            # 获取数据URL（模拟）
            if input_method == "文件上传" and file_upload:
                file_url = f"https://temp-storage.example.com/{file_upload.name}"
            elif input_method == "URL导入" and url_input:
                file_url = url_input
            elif input_method == "直接输入" and direct_input:
                file_url = "https://temp-storage.example.com/direct_input.csv"
            else:
                return display_manager.format_analysis_result({
                    "result": "请提供有效的数据输入",
                    "title": "输入错误"
                })
            
            # 调用模拟API
            result = mock_api.simulate_chat_analysis(prompt, file_url)
            
            # 格式化结果
            return display_manager.format_analysis_result(result)
        
        def handle_chart_creation(data_input, chart_prompt):
            """处理图表生成请求"""
            
            # 模拟API调用延迟
            time.sleep(1)
            
            if not data_input or not chart_prompt:
                return """
                <div style="background: #fee2e2; border-radius: 12px; padding: 2rem; margin: 1rem 0; color: #991b1b;">
                    <h3>❌ 输入错误</h3>
                    <p>请提供数据和图表需求描述</p>
                </div>
                """
            
            # 调用模拟API
            chart_data = mock_api.simulate_chart_creation(chart_prompt, "temp_data_url")
            
            # 格式化结果
            status_html, chart_preview = display_manager.format_chart_result(chart_data)
            
            return status_html + chart_preview
        
        # 绑定事件
        input_method.change(
            handle_input_method_change,
            inputs=[input_method],
            outputs=[file_upload, url_input, direct_input]
        )
        
        analyze_btn.click(
            handle_analysis,
            inputs=[input_method, file_upload, url_input, direct_input, analysis_prompt],
            outputs=[analysis_result]
        )
        
        create_chart_btn.click(
            handle_chart_creation,
            inputs=[chart_data_input, chart_prompt],
            outputs=[chart_result]
        )
    
    return app

if __name__ == "__main__":
    app = create_demo_app()
    app.launch(
        server_name="0.0.0.0",
        server_port=7861,
        share=False,
        show_error=True
    )
