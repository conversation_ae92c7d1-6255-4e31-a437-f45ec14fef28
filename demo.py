#!/usr/bin/env python3
"""
Demo script for the AiBiao AI Data Analysis System.
This creates a simplified version for testing without requiring a full API key.
"""

import gradio as gr
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import tempfile
import os

def create_sample_data():
    """Create sample data for demonstration."""
    np.random.seed(42)
    
    # Sample sales data
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    products = ['Laptop', 'Mouse', 'Keyboard', 'Monitor', 'Headphones']
    regions = ['North', 'South', 'East', 'West']
    
    data = []
    for i in range(500):
        data.append({
            'Date': np.random.choice(dates),
            'Product': np.random.choice(products),
            'Region': np.random.choice(regions),
            'Sales': np.random.randint(1000, 10000),
            'Quantity': np.random.randint(1, 50),
            'Customer_Rating': np.random.uniform(3.0, 5.0)
        })
    
    return pd.DataFrame(data)

def process_data_input(input_method, file_upload, url_input, direct_input):
    """Process data input from various sources."""
    try:
        if input_method == "Sample Data":
            df = create_sample_data()
            return True, df, "Sample data loaded successfully!"
        
        elif input_method == "File Upload" and file_upload:
            if file_upload.name.endswith('.csv'):
                df = pd.read_csv(file_upload.name)
            elif file_upload.name.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(file_upload.name)
            else:
                return False, None, "Unsupported file format"
            return True, df, f"File loaded successfully! Shape: {df.shape}"
        
        elif input_method == "Direct Input" and direct_input:
            from io import StringIO
            df = pd.read_csv(StringIO(direct_input))
            return True, df, f"Data parsed successfully! Shape: {df.shape}"
        
        else:
            return False, None, "Please provide data input"
            
    except Exception as e:
        return False, None, f"Error processing data: {str(e)}"

def create_analysis_charts(df, analysis_prompt):
    """Create charts based on the data and analysis prompt."""
    if df is None or df.empty:
        return None, "No data available for analysis"
    
    try:
        # Basic data summary
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
        
        # Create different charts based on prompt keywords
        prompt_lower = analysis_prompt.lower()
        
        if 'trend' in prompt_lower or 'time' in prompt_lower:
            # Time series analysis
            if 'date' in df.columns:
                df_sorted = df.sort_values('Date')
                fig = px.line(df_sorted, x='Date', y=numeric_cols[0] if numeric_cols else 'Sales',
                             title=f"Trend Analysis: {numeric_cols[0] if numeric_cols else 'Sales'} over Time")
            else:
                fig = px.line(df.head(20), y=numeric_cols[0] if numeric_cols else df.columns[0],
                             title="Trend Analysis")
        
        elif 'distribution' in prompt_lower or 'histogram' in prompt_lower:
            # Distribution analysis
            col = numeric_cols[0] if numeric_cols else df.columns[0]
            fig = px.histogram(df, x=col, title=f"Distribution of {col}")
        
        elif 'comparison' in prompt_lower or 'compare' in prompt_lower:
            # Comparison analysis
            if len(categorical_cols) > 0 and len(numeric_cols) > 0:
                fig = px.bar(df.groupby(categorical_cols[0])[numeric_cols[0]].mean().reset_index(),
                           x=categorical_cols[0], y=numeric_cols[0],
                           title=f"Comparison: Average {numeric_cols[0]} by {categorical_cols[0]}")
            else:
                fig = px.bar(df.head(10), x=df.columns[0], y=df.columns[1] if len(df.columns) > 1 else df.columns[0])
        
        elif 'correlation' in prompt_lower:
            # Correlation analysis
            if len(numeric_cols) >= 2:
                corr_matrix = df[numeric_cols].corr()
                fig = px.imshow(corr_matrix, text_auto=True, aspect="auto",
                              title="Correlation Matrix")
            else:
                fig = px.scatter(df, x=numeric_cols[0] if numeric_cols else df.columns[0],
                               y=numeric_cols[1] if len(numeric_cols) > 1 else df.columns[1],
                               title="Correlation Analysis")
        
        else:
            # Default: create a summary chart
            if len(categorical_cols) > 0 and len(numeric_cols) > 0:
                fig = px.bar(df.groupby(categorical_cols[0])[numeric_cols[0]].sum().head(10).reset_index(),
                           x=categorical_cols[0], y=numeric_cols[0],
                           title=f"Summary: {numeric_cols[0]} by {categorical_cols[0]}")
            else:
                fig = px.histogram(df, x=df.columns[0], title=f"Distribution of {df.columns[0]}")
        
        # Update layout for better appearance
        fig.update_layout(
            template="plotly_white",
            height=500,
            font=dict(size=12)
        )
        
        # Generate analysis summary
        summary = f"""
        📊 **Analysis Complete!**
        
        **Data Overview:**
        - Rows: {df.shape[0]:,}
        - Columns: {df.shape[1]}
        - Numeric columns: {len(numeric_cols)}
        - Categorical columns: {len(categorical_cols)}
        
        **Analysis Request:** {analysis_prompt}
        
        **Key Insights:**
        - Chart type: {fig.data[0].type.title()} chart
        - Data processed successfully
        - Visualization generated based on your request
        """
        
        return fig, summary
        
    except Exception as e:
        return None, f"Error creating analysis: {str(e)}"

def create_demo_interface():
    """Create the demo Gradio interface."""
    
    # Custom CSS
    custom_css = """
    .gradio-container {
        max-width: 1200px !important;
        margin: auto !important;
    }
    .main-header {
        text-align: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    .feature-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
    }
    """
    
    with gr.Blocks(
        theme=gr.themes.Soft(
            primary_hue="blue",
            secondary_hue="purple",
            neutral_hue="slate"
        ),
        css=custom_css,
        title="AiBiao AI Data Analysis System - Demo"
    ) as demo:
        
        # Header
        gr.HTML("""
        <div class="main-header">
            <h1>🤖 AiBiao AI Data Analysis System</h1>
            <p>Modern AI-powered data analysis with intelligent chart generation</p>
            <p><em>Demo Version - Experience the interface and features</em></p>
        </div>
        """)
        
        # State variables
        current_data = gr.State(None)
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.HTML("<h3>📊 Data Input</h3>")
                
                # Input method selection
                input_method = gr.Radio(
                    choices=["Sample Data", "File Upload", "Direct Input"],
                    value="Sample Data",
                    label="Input Method"
                )
                
                # File upload
                file_upload = gr.File(
                    label="Upload Data File",
                    file_types=[".csv", ".xlsx", ".xls"],
                    visible=False
                )
                
                # Direct input
                direct_input = gr.Textbox(
                    label="Paste CSV Data",
                    placeholder="Name,Age,City\nJohn,25,NYC\nJane,30,LA",
                    lines=8,
                    visible=False
                )
                
                # Analysis prompt
                analysis_prompt = gr.Textbox(
                    label="Analysis Request",
                    placeholder="What would you like to analyze? (e.g., 'Show sales trends', 'Compare regions', 'Distribution analysis')",
                    lines=3,
                    value="Show sales trends over time"
                )
                
                # Analyze button
                analyze_btn = gr.Button("🚀 Start Analysis", variant="primary", size="lg")
                
                # Load sample data button
                sample_btn = gr.Button("📋 Load Sample Data", variant="secondary")
            
            with gr.Column(scale=2):
                gr.HTML("<h3>📈 Analysis Results</h3>")
                
                # Status display
                status_display = gr.Markdown("Ready to analyze data...")
                
                # Chart display
                chart_display = gr.Plot(label="Generated Chart")
                
                # Data preview
                data_preview = gr.Dataframe(
                    label="Data Preview",
                    interactive=False
                )
        
        # Event handlers
        def handle_input_method_change(method):
            return (
                gr.update(visible=(method == "File Upload")),
                gr.update(visible=(method == "Direct Input"))
            )
        
        def handle_analysis(input_method, file_upload, url_input, direct_input, analysis_prompt, current_data):
            # Process data input
            success, df, message = process_data_input(input_method, file_upload, url_input, direct_input)
            
            if not success:
                return message, None, None, current_data
            
            # Create analysis
            chart, analysis_summary = create_analysis_charts(df, analysis_prompt)
            
            # Data preview
            preview_df = df.head(10) if df is not None else None
            
            return analysis_summary, chart, preview_df, df
        
        def load_sample_data():
            df = create_sample_data()
            return df.head(10), df
        
        # Connect events
        input_method.change(
            handle_input_method_change,
            inputs=[input_method],
            outputs=[file_upload, direct_input]
        )
        
        analyze_btn.click(
            handle_analysis,
            inputs=[input_method, file_upload, gr.State(None), direct_input, analysis_prompt, current_data],
            outputs=[status_display, chart_display, data_preview, current_data]
        )
        
        sample_btn.click(
            load_sample_data,
            outputs=[data_preview, current_data]
        )
        
        # Footer
        gr.HTML("""
        <div style="text-align: center; margin-top: 2rem; padding: 1rem; background-color: #f8f9fa; border-radius: 8px;">
            <p><strong>AiBiao AI Data Analysis System - Demo Version</strong></p>
            <p>This demo showcases the interface and basic functionality. For full AI-powered analysis, configure your AiBiao API key in the production version.</p>
            <p>📚 <a href="README.md">View Documentation</a> | 🔧 <a href="https://aibiao.cn/public/api-key">Get API Key</a></p>
        </div>
        """)
    
    return demo

if __name__ == "__main__":
    demo = create_demo_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True
    )
