# AiBiao AI Data Analysis System

A modern, professional AI-powered data analysis system built with Gradio and integrated with the AiBiao API. This system provides an intuitive web interface for data analysis, visualization, and AI-powered insights.

> 🚀 **This project uses [uv](https://docs.astral.sh/uv/)** - An extremely fast Python package and project manager, written in Rust.

## 🌟 Features

### Core Functionality
- **Multi-format Data Input**: Support for Excel (.xlsx, .xls), CSV, and JSON files
- **Flexible Data Sources**: File upload, URL import, or direct data input
- **AI-Powered Analysis**: Integration with AiBiao's AI analysis endpoints
- **Interactive Visualizations**: Dynamic charts and graphs using Plotly
- **Real-time Processing**: Live data processing and analysis feedback

### AI Integration
- **Chat-based Analysis**: Natural language queries for data insights
- **Automated Chart Generation**: AI-powered chart creation with custom prompts
- **Workflow Management**: Save and reuse analysis workflows
- **Smart Suggestions**: AI-generated follow-up questions and insights

### User Interface
- **Modern Design**: Clean, professional interface with responsive layout
- **Tabbed Navigation**: Organized sections for different functionalities
- **Real-time Status**: Live connection status and processing feedback
- **Export Options**: Multiple export formats for data and visualizations

## 🚀 Quick Start

### Prerequisites
- Python 3.11 or higher
- [uv](https://docs.astral.sh/uv/) package manager
- AiBiao API key (get one at [aibiao.cn](https://aibiao.cn/public/api-key))

### Installation

1. **Install uv** (if not already installed):
   ```bash
   # macOS and Linux
   curl -LsSf https://astral.sh/uv/install.sh | sh

   # Windows
   powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

   # Or via pip
   pip install uv
   ```

2. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd aibiao_demo
   ```

3. **Install dependencies with uv**:
   ```bash
   uv sync
   ```

4. **Set up environment variables** (optional):
   ```bash
   echo "AIBIAO_API_KEY=your_api_key_here" > .env
   ```

5. **Run the application**:
   ```bash
   uv run python main.py
   ```

6. **Open your browser** and navigate to `http://localhost:7860`

### Alternative: Quick Demo (No API Key Required)
```bash
uv run python demo.py
```

## 📖 Usage Guide

### 1. API Configuration
- Enter your AiBiao API key in the configuration section
- Click "Connect" to establish connection
- Use "Test Connection" to verify your API key status

### 2. Data Analysis
- **File Upload**: Upload Excel, CSV, or JSON files
- **URL Import**: Provide a direct URL to your data file
- **Direct Input**: Paste CSV data directly into the interface
- Enter your analysis request in natural language
- Click "Start Analysis" to begin processing

### 3. Chat Sessions
- View and manage your analysis chat sessions
- Continue conversations with follow-up questions
- Access chat history and previous analyses

### 4. Charts Gallery
- Browse all your generated charts
- Preview charts directly in the interface
- Access shareable links for your visualizations

### 5. Workflows
- Save successful analysis patterns as workflows
- Execute workflows with new datasets
- Manage and organize your analysis templates

## 🔧 Configuration

### Environment Variables
```bash
AIBIAO_API_KEY=your_api_key_here  # Your AiBiao API key
```

### File Limits
- Maximum file size: 100MB
- Supported formats: .xlsx, .xls, .csv, .json
- Maximum rows: 1,000,000
- Maximum columns: 1,000

## 📊 API Integration

The system integrates with the following AiBiao API endpoints:

### System Management
- `GET /api-key-status` - Check API key status and quota

### Chat Management
- `POST /new-chat` - Create new chat session
- `POST /append-chat` - Add messages to chat
- `POST /list-chats` - List all chat sessions
- `POST /get-chat` - Get specific chat details
- `POST /update-chat-title` - Update chat title
- `POST /delete-chat` - Delete chat session

### Chart Management
- `POST /new-chart` - Create new chart
- `POST /list-charts` - List all charts
- `POST /delete-chart` - Delete chart

### Workflow Management
- `POST /save-workflow` - Save analysis workflow
- `POST /list-workflows` - List all workflows
- `POST /execute-workflow` - Execute workflow
- `POST /update-workflow-name` - Update workflow details
- `POST /delete-workflow` - Delete workflow

## 🛠️ Development

### Project Structure
```
aibiao_demo/
├── main.py                 # Application entry point
├── demo.py                 # Standalone demo version
├── test_system.py          # Test suite
├── src/
│   ├── __init__.py
│   ├── config.py          # Configuration management
│   ├── aibiao_client.py   # API client implementation
│   ├── ui_components.py   # Gradio UI components
│   ├── data_processor.py  # Data processing utilities
│   ├── visualization.py   # Chart generation
│   ├── event_handlers.py  # UI event handlers
│   ├── results_display.py # Results formatting
│   └── error_handling.py  # Error handling & validation
├── pyproject.toml         # Project dependencies (uv managed)
├── .env.example           # Environment variables template
└── README.md             # This file
```

### Development Setup

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd aibiao_demo
   uv sync
   ```

2. **Run tests**:
   ```bash
   uv run python test_system.py
   ```

3. **Development mode**:
   ```bash
   # Run with auto-reload
   uv run python main.py

   # Or run demo version
   uv run python demo.py
   ```

4. **Add new dependencies**:
   ```bash
   uv add package-name
   ```

5. **Update dependencies**:
   ```bash
   uv sync --upgrade
   ```

### Key Components

#### AiBiaoClient
Handles all API interactions with the AiBiao service, including authentication, request formatting, and response parsing.

#### DataProcessor
Manages data input, validation, and preprocessing for various file formats and input methods.

#### ChartGenerator
Creates interactive visualizations using Plotly, with support for multiple chart types and customization options.

#### ErrorHandler
Provides comprehensive error handling, input validation, and user-friendly error messages.

## 🔒 Security

- API keys are handled securely and not logged
- Input validation prevents malicious data injection
- File size limits prevent resource exhaustion
- Comprehensive error handling prevents information leakage

## 🐛 Troubleshooting

### Common Issues

1. **uv command not found**
   - Install uv: `curl -LsSf https://astral.sh/uv/install.sh | sh`
   - Restart your terminal
   - Or use pip: `pip install uv`

2. **API Connection Failed**
   - Verify your API key is correct
   - Check your internet connection
   - Ensure the API key has sufficient quota

3. **File Upload Errors**
   - Check file size (max 100MB)
   - Verify file format is supported
   - Ensure file is not corrupted

4. **Analysis Errors**
   - Verify data format is correct
   - Check for empty or invalid data
   - Ensure analysis prompt is clear and specific

5. **Dependencies Issues**
   - Run `uv sync` to reinstall dependencies
   - Check Python version: `python --version` (requires 3.11+)
   - Clear cache: `uv cache clean`

### Logs
Application logs are saved to `aibiao_system.log` for debugging purposes.

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Setup development environment: `uv sync`
4. Make your changes
5. Run tests: `uv run python test_system.py`
6. Add tests if applicable
7. Submit a pull request

### Development Commands
```bash
# Install dependencies
uv sync

# Run application
uv run python main.py

# Run tests
uv run python test_system.py

# Run demo
uv run python demo.py

# Add new dependency
uv add package-name

# Add development dependency
uv add --dev package-name
```

## 📞 Support

For support and questions:
- Check the [AiBiao documentation](https://aibiao.cn/public/api-key)
- Review the troubleshooting section above
- Open an issue in the repository

## 🔄 Updates

### Version 1.0.0
- Initial release with core functionality
- AiBiao API integration
- Modern Gradio interface
- Comprehensive error handling
- Multi-format data support
- **uv package manager integration** for fast dependency management
- Standalone demo version for testing without API key

### Why uv?
- ⚡ **10-100x faster** than pip for dependency resolution and installation
- 🔒 **Deterministic builds** with automatic lock file generation
- 🛠️ **Project management** with built-in virtual environment handling
- 🦀 **Written in Rust** for maximum performance and reliability
