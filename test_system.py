#!/usr/bin/env python3
"""
Test script for the AiBiao AI Data Analysis System.
"""

import sys
import os
from pathlib import Path
import pandas as pd
import tempfile

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from aibiao_client import AiBiaoClient
from data_processor import DataProcessor
from visualization import ChartGenerator
from error_handling import <PERSON>rror<PERSON>and<PERSON>
from config import Config

def test_data_processor():
    """Test data processing functionality."""
    print("🧪 Testing Data Processor...")
    
    processor = DataProcessor()
    
    # Test CSV data processing
    csv_data = """Name,Age,City,Salary
John,25,New York,50000
Jane,30,Los Angeles,60000
Bob,35,Chicago,55000
Alice,28,Houston,52000"""
    
    success, result = processor.process_direct_input(csv_data)
    
    if success:
        print("✅ CSV processing: PASSED")
        print(f"   Data shape: {result.shape}")
        print(f"   Columns: {list(result.columns)}")
    else:
        print(f"❌ CSV processing: FAILED - {result}")
    
    # Test data summary
    if success:
        summary = processor.get_data_summary(result)
        if "shape" in summary:
            print("✅ Data summary: PASSED")
        else:
            print("❌ Data summary: FAILED")

def test_chart_generator():
    """Test chart generation functionality."""
    print("\n🧪 Testing Chart Generator...")
    
    generator = ChartGenerator()
    
    # Create sample data
    df = pd.DataFrame({
        'Category': ['A', 'B', 'C', 'D'],
        'Values': [10, 20, 15, 25],
        'Values2': [5, 15, 10, 20]
    })
    
    try:
        # Test bar chart
        fig = generator.create_basic_chart(df, 'bar', x_col='Category', y_col='Values')
        if fig:
            print("✅ Bar chart generation: PASSED")
        else:
            print("❌ Bar chart generation: FAILED")
        
        # Test chart suggestions
        suggestions = generator.suggest_chart_types(df)
        if suggestions:
            print("✅ Chart suggestions: PASSED")
            print(f"   Found {len(suggestions)} suggestions")
        else:
            print("❌ Chart suggestions: FAILED")
            
    except Exception as e:
        print(f"❌ Chart generation: FAILED - {str(e)}")

def test_error_handler():
    """Test error handling functionality."""
    print("\n🧪 Testing Error Handler...")
    
    handler = ErrorHandler()
    
    # Test API key validation
    valid_key = "sk-1234567890abcdef1234567890abcdef12"
    invalid_key = "invalid-key"
    
    is_valid, message = handler.validate_api_key(valid_key)
    if is_valid:
        print("✅ Valid API key validation: PASSED")
    else:
        print(f"❌ Valid API key validation: FAILED - {message}")
    
    is_valid, message = handler.validate_api_key(invalid_key)
    if not is_valid:
        print("✅ Invalid API key validation: PASSED")
    else:
        print("❌ Invalid API key validation: FAILED")
    
    # Test URL validation
    valid_url = "https://example.com/data.csv"
    invalid_url = "not-a-url"
    
    is_valid, message = handler.validate_url(valid_url)
    if is_valid:
        print("✅ Valid URL validation: PASSED")
    else:
        print(f"❌ Valid URL validation: FAILED - {message}")
    
    is_valid, message = handler.validate_url(invalid_url)
    if not is_valid:
        print("✅ Invalid URL validation: PASSED")
    else:
        print("❌ Invalid URL validation: FAILED")

def test_config():
    """Test configuration management."""
    print("\n🧪 Testing Configuration...")
    
    config = Config()
    
    # Test basic config properties
    if hasattr(config, 'api_base_url'):
        print("✅ Config initialization: PASSED")
    else:
        print("❌ Config initialization: FAILED")
    
    # Test API key methods
    test_key = "test-key-123"
    config.set_api_key(test_key)
    
    if config.get_api_key() == test_key:
        print("✅ API key management: PASSED")
    else:
        print("❌ API key management: FAILED")

def test_aibiao_client():
    """Test AiBiao client (without actual API calls)."""
    print("\n🧪 Testing AiBiao Client...")
    
    client = AiBiaoClient()
    
    # Test client initialization
    if hasattr(client, 'api_key') and hasattr(client, 'base_url'):
        print("✅ Client initialization: PASSED")
    else:
        print("❌ Client initialization: FAILED")
    
    # Test API key setting
    test_key = "sk-test123456789"
    client.set_api_key(test_key)
    
    if client.api_key == test_key:
        print("✅ API key setting: PASSED")
    else:
        print("❌ API key setting: FAILED")
    
    print("ℹ️  Note: Actual API calls require a valid API key")

def create_sample_data_file():
    """Create a sample data file for testing."""
    print("\n📁 Creating sample data file...")
    
    # Create sample data
    data = {
        'Product': ['Laptop', 'Mouse', 'Keyboard', 'Monitor', 'Headphones'],
        'Price': [999.99, 29.99, 79.99, 299.99, 149.99],
        'Category': ['Electronics', 'Accessories', 'Accessories', 'Electronics', 'Accessories'],
        'Stock': [50, 200, 150, 75, 100],
        'Rating': [4.5, 4.2, 4.0, 4.7, 4.3]
    }
    
    df = pd.DataFrame(data)
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        df.to_csv(f.name, index=False)
        print(f"✅ Sample data file created: {f.name}")
        print(f"   Data shape: {df.shape}")
        print(f"   Columns: {list(df.columns)}")
        return f.name

def main():
    """Run all tests."""
    print("🚀 AiBiao AI Data Analysis System - Test Suite")
    print("=" * 50)
    
    # Run tests
    test_config()
    test_error_handler()
    test_data_processor()
    test_chart_generator()
    test_aibiao_client()
    
    # Create sample data
    sample_file = create_sample_data_file()
    
    print("\n" + "=" * 50)
    print("🎉 Test Suite Complete!")
    print("\nNext steps:")
    print("1. Set up your API key in .env file (copy from .env.example)")
    print("2. Run the application: python main.py")
    print("3. Open http://localhost:7860 in your browser")
    print(f"4. Use the sample data file for testing: {sample_file}")
    
    print("\n📚 For more information, see README.md")

if __name__ == "__main__":
    main()
