#!/usr/bin/env python3
"""
AiBiao AI Data Analysis System
A modern Gradio-based interface for AI-powered data analysis using the AiBiao API.
"""

import gradio as gr
import os
import sys
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from aibiao_client import AiBiaoClient
from ui_components import create_main_interface
from config import load_config

def main():
    """Main entry point for the application."""
    # Load configuration
    config = load_config()
    
    # Initialize the AiBiao client
    client = AiBiaoClient()
    
    # Create the main interface
    app = create_main_interface(client, config)
    
    # Launch the application
    app.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True,
        favicon_path="assets/favicon.ico" if os.path.exists("assets/favicon.ico") else None
    )

if __name__ == "__main__":
    main()
